package com.geeksec.admin.domain.model;

import com.geeksec.admin.domain.event.DomainEvent;

import java.util.ArrayList;
import java.util.List;

/**
 * 聚合根基类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public abstract class AggregateRoot<ID> {
    
    /**
     * 领域事件列表
     */
    private List<DomainEvent> domainEvents = new ArrayList<>();
    
    /**
     * 获取聚合根ID
     * 
     * @return 聚合根ID
     */
    public abstract ID getId();
    
    /**
     * 获取聚合根类型
     * 
     * @return 聚合根类型
     */
    public String getAggregateType() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 添加领域事件
     * 
     * @param event 领域事件
     */
    protected void addDomainEvent(DomainEvent event) {
        this.domainEvents.add(event);
    }
    
    /**
     * 获取领域事件列表
     * 
     * @return 领域事件列表
     */
    public List<DomainEvent> getDomainEvents() {
        return new ArrayList<>(this.domainEvents);
    }
    
    /**
     * 清除领域事件
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }
    
    /**
     * 检查是否有未发布的领域事件
     * 
     * @return 是否有未发布的事件
     */
    public boolean hasUnpublishedEvents() {
        return !this.domainEvents.isEmpty();
    }
}
