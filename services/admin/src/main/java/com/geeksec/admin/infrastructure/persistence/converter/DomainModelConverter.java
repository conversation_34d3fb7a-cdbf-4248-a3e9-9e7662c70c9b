package com.geeksec.admin.infrastructure.persistence.converter;

import com.alibaba.fastjson2.JSON;
import com.geeksec.admin.domain.model.DataLifecycleConfig;
import com.geeksec.admin.domain.model.MaintenanceTask;
import com.geeksec.admin.infrastructure.persistence.entity.DataLifecycleConfigDO;
import com.geeksec.admin.infrastructure.persistence.entity.MaintenanceTaskDO;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 领域模型与DO对象转换器
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class DomainModelConverter {
    
    /**
     * 领域模型转换为DO对象
     * 
     * @param config 数据生命周期配置
     * @return DO对象
     */
    public DataLifecycleConfigDO toDataObject(DataLifecycleConfig config) {
        if (config == null) {
            return null;
        }
        
        return new DataLifecycleConfigDO()
                .setConfigId(config.getConfigId())
                .setTableName(config.getTableName())
                .setRetentionDays(config.getRetentionDays())
                .setAutoPartitionEnabled(config.getAutoPartitionEnabled())
                .setPartitionColumn(config.getPartitionColumn())
                .setPartitionGranularity(config.getPartitionGranularity() != null ? 
                        config.getPartitionGranularity().name() : null)
                .setHotPartitionNum(config.getHotPartitionNum())
                .setWarmPartitionNum(config.getWarmPartitionNum())
                .setColdPartitionNum(config.getColdPartitionNum())
                .setAutoCleanupEnabled(config.getAutoCleanupEnabled())
                .setCompressionEnabled(config.getCompressionEnabled())
                .setCompressionDelayHours(config.getCompressionDelayHours())
                .setCompressionThreshold(config.getCompressionThreshold())
                .setCreateTime(config.getCreateTime())
                .setUpdateTime(config.getUpdateTime())
                .setCreatedBy(config.getCreatedBy())
                .setUpdatedBy(config.getUpdatedBy());
    }
    
    /**
     * DO对象转换为领域模型
     * 
     * @param configDO DO对象
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig toDomainModel(DataLifecycleConfigDO configDO) {
        if (configDO == null) {
            return null;
        }
        
        return new DataLifecycleConfig()
                .setConfigId(configDO.getConfigId())
                .setTableName(configDO.getTableName())
                .setRetentionDays(configDO.getRetentionDays())
                .setAutoPartitionEnabled(configDO.getAutoPartitionEnabled())
                .setPartitionColumn(configDO.getPartitionColumn())
                .setPartitionGranularity(configDO.getPartitionGranularity() != null ? 
                        DataLifecycleConfig.PartitionGranularity.valueOf(configDO.getPartitionGranularity()) : null)
                .setHotPartitionNum(configDO.getHotPartitionNum())
                .setWarmPartitionNum(configDO.getWarmPartitionNum())
                .setColdPartitionNum(configDO.getColdPartitionNum())
                .setAutoCleanupEnabled(configDO.getAutoCleanupEnabled())
                .setCompressionEnabled(configDO.getCompressionEnabled())
                .setCompressionDelayHours(configDO.getCompressionDelayHours())
                .setCompressionThreshold(configDO.getCompressionThreshold())
                .setCreateTime(configDO.getCreateTime())
                .setUpdateTime(configDO.getUpdateTime())
                .setCreatedBy(configDO.getCreatedBy())
                .setUpdatedBy(configDO.getUpdatedBy());
    }
    
    /**
     * 领域模型转换为DO对象
     * 
     * @param task 维护任务
     * @return DO对象
     */
    public MaintenanceTaskDO toDataObject(MaintenanceTask task) {
        if (task == null) {
            return null;
        }
        
        return new MaintenanceTaskDO()
                .setTaskId(task.getTaskId())
                .setTaskName(task.getTaskName())
                .setTaskType(task.getTaskType() != null ? task.getTaskType().name() : null)
                .setStatus(task.getStatus() != null ? task.getStatus().name() : null)
                .setPriority(task.getPriority() != null ? task.getPriority().name() : null)
                .setTargetTable(task.getTargetTable())
                .setTaskConfig(task.getTaskConfig() != null ? JSON.toJSONString(task.getTaskConfig()) : null)
                .setScheduledTime(task.getScheduledTime())
                .setStartTime(task.getStartTime())
                .setEndTime(task.getEndTime())
                .setProgress(task.getProgress())
                .setResult(task.getResult())
                .setErrorMessage(task.getErrorMessage())
                .setCreateTime(task.getCreateTime())
                .setCreatedBy(task.getCreatedBy())
                .setUpdateTime(task.getUpdateTime())
                .setUpdatedBy(task.getUpdatedBy());
    }
    
    /**
     * DO对象转换为领域模型
     * 
     * @param taskDO DO对象
     * @return 维护任务
     */
    public MaintenanceTask toDomainModel(MaintenanceTaskDO taskDO) {
        if (taskDO == null) {
            return null;
        }
        
        return new MaintenanceTask()
                .setTaskId(taskDO.getTaskId())
                .setTaskName(taskDO.getTaskName())
                .setTaskType(taskDO.getTaskType() != null ? 
                        MaintenanceTask.TaskType.valueOf(taskDO.getTaskType()) : null)
                .setStatus(taskDO.getStatus() != null ? 
                        MaintenanceTask.TaskStatus.valueOf(taskDO.getStatus()) : null)
                .setPriority(taskDO.getPriority() != null ? 
                        MaintenanceTask.TaskPriority.valueOf(taskDO.getPriority()) : null)
                .setTargetTable(taskDO.getTargetTable())
                .setTaskConfig(taskDO.getTaskConfig() != null ? 
                        JSON.parseObject(taskDO.getTaskConfig(), Map.class) : null)
                .setScheduledTime(taskDO.getScheduledTime())
                .setStartTime(taskDO.getStartTime())
                .setEndTime(taskDO.getEndTime())
                .setProgress(taskDO.getProgress())
                .setResult(taskDO.getResult())
                .setErrorMessage(taskDO.getErrorMessage())
                .setCreateTime(taskDO.getCreateTime())
                .setCreatedBy(taskDO.getCreatedBy())
                .setUpdateTime(taskDO.getUpdateTime())
                .setUpdatedBy(taskDO.getUpdatedBy());
    }
}
