package com.geeksec.admin.infrastructure.persistence.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 数据生命周期配置数据对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
@Table("data_lifecycle_config")
public class DataLifecycleConfigDO {
    
    /**
     * 配置ID
     */
    @Id(keyType = KeyType.Generator, value = "uuid")
    private String configId;
    
    /**
     * 表名
     */
    private String tableName;
    
    /**
     * 数据保留天数
     */
    private Integer retentionDays;
    
    /**
     * 是否启用自动分区
     */
    private Boolean autoPartitionEnabled;
    
    /**
     * 分区列名
     */
    private String partitionColumn;
    
    /**
     * 分区粒度
     */
    private String partitionGranularity;
    
    /**
     * 热分区数量
     */
    private Integer hotPartitionNum;
    
    /**
     * 温分区数量
     */
    private Integer warmPartitionNum;
    
    /**
     * 冷分区数量
     */
    private Integer coldPartitionNum;
    
    /**
     * 是否启用自动清理
     */
    private Boolean autoCleanupEnabled;
    
    /**
     * 是否启用压缩
     */
    private Boolean compressionEnabled;
    
    /**
     * 压缩延迟小时数
     */
    private Integer compressionDelayHours;
    
    /**
     * 压缩阈值
     */
    private Double compressionThreshold;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新者
     */
    private String updatedBy;
}
