package com.geeksec.admin.application.converter;

import com.geeksec.admin.domain.model.DataLifecycleConfig;
import com.geeksec.admin.domain.model.MaintenanceTask;
import com.geeksec.admin.domain.model.PartitionInfo;
import com.geeksec.admin.domain.model.StorageStats;
import com.geeksec.admin.interfaces.dto.DataLifecycleConfigDto;
import com.geeksec.admin.interfaces.dto.MaintenanceTaskDto;
import org.springframework.stereotype.Component;

import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据维护转换器 - 负责领域模型与DTO之间的转换
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class DataMaintenanceConverter {
    
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    
    /**
     * 分区信息转换为Map
     * 
     * @param partition 分区信息
     * @return Map格式的分区信息
     */
    public Map<String, Object> partitionInfoToMap(PartitionInfo partition) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("partition_name", partition.getPartitionName());
        map.put("table_name", partition.getTableName());
        map.put("partition_type", partition.getPartitionType());
        map.put("partition_key", partition.getPartitionKey());
        map.put("partition_value", partition.getPartitionValue());
        map.put("row_count", partition.getRowCount());
        map.put("data_size_bytes", partition.getDataSizeBytes());
        map.put("index_size_bytes", partition.getIndexSizeBytes());
        map.put("create_time", partition.getCreateTime() != null ? 
                partition.getCreateTime().format(DATE_TIME_FORMATTER) : null);
        map.put("last_update_time", partition.getLastUpdateTime() != null ? 
                partition.getLastUpdateTime().format(DATE_TIME_FORMATTER) : null);
        map.put("is_active", partition.getIsActive());
        map.put("compression_ratio", partition.getCompressionRatio());
        return map;
    }
    
    /**
     * 维护任务转换为Map
     * 
     * @param task 维护任务
     * @return Map格式的任务信息
     */
    public Map<String, Object> maintenanceTaskToMap(MaintenanceTask task) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("task_id", task.getTaskId());
        map.put("task_name", task.getTaskName());
        map.put("task_type", task.getTaskType().name());
        map.put("task_type_desc", task.getTaskType().getDescription());
        map.put("status", task.getStatus().name());
        map.put("status_desc", task.getStatus().getDescription());
        map.put("priority", task.getPriority().name());
        map.put("priority_desc", task.getPriority().getDescription());
        map.put("target_table", task.getTargetTable());
        map.put("task_config", task.getTaskConfig());
        map.put("scheduled_time", task.getScheduledTime() != null ? 
                task.getScheduledTime().format(DATE_TIME_FORMATTER) : null);
        map.put("start_time", task.getStartTime() != null ? 
                task.getStartTime().format(DATE_TIME_FORMATTER) : null);
        map.put("end_time", task.getEndTime() != null ? 
                task.getEndTime().format(DATE_TIME_FORMATTER) : null);
        map.put("progress", task.getProgress());
        map.put("result", task.getResult());
        map.put("error_message", task.getErrorMessage());
        map.put("created_by", task.getCreatedBy());
        map.put("create_time", task.getCreateTime() != null ? 
                task.getCreateTime().format(DATE_TIME_FORMATTER) : null);
        return map;
    }
    
    /**
     * 存储统计信息转换为Map
     * 
     * @param stats 存储统计信息
     * @return Map格式的统计信息
     */
    public Map<String, Object> storageStatsToMap(StorageStats stats) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("total_storage_bytes", stats.getTotalStorageBytes());
        map.put("used_storage_bytes", stats.getUsedStorageBytes());
        map.put("available_storage_bytes", stats.getAvailableStorageBytes());
        map.put("usage_percentage", stats.getUsagePercentage());
        map.put("session_logs_size_bytes", stats.getSessionLogsSizeBytes());
        map.put("indexes_size_bytes", stats.getIndexesSizeBytes());
        map.put("temp_files_size_bytes", stats.getTempFilesSizeBytes());
        map.put("backup_size_bytes", stats.getBackupSizeBytes());
        map.put("growth_rate_bytes_per_day", stats.getGrowthRateBytesPerDay());
        map.put("estimated_full_date", stats.getEstimatedFullDate() != null ? 
                stats.getEstimatedFullDate().format(DATE_TIME_FORMATTER) : null);
        map.put("health_status", stats.getHealthStatus() != null ? 
                stats.getHealthStatus().name() : null);
        map.put("stats_time", stats.getStatsTime() != null ? 
                stats.getStatsTime().format(DATE_TIME_FORMATTER) : null);
        return map;
    }
    
    /**
     * 数据生命周期配置转换为Map
     * 
     * @param config 数据生命周期配置
     * @return Map格式的配置信息
     */
    public Map<String, Object> dataLifecycleConfigToMap(DataLifecycleConfig config) {
        Map<String, Object> map = new HashMap<>(16);
        map.put("config_id", config.getConfigId());
        map.put("table_name", config.getTableName());
        map.put("retention_days", config.getRetentionDays());
        map.put("auto_partition_enabled", config.getAutoPartitionEnabled());
        map.put("partition_column", config.getPartitionColumn());
        map.put("partition_granularity", config.getPartitionGranularity() != null ? 
                config.getPartitionGranularity().name() : null);
        map.put("hot_partition_num", config.getHotPartitionNum());
        map.put("warm_partition_num", config.getWarmPartitionNum());
        map.put("cold_partition_num", config.getColdPartitionNum());
        map.put("auto_cleanup_enabled", config.getAutoCleanupEnabled());
        map.put("compression_enabled", config.getCompressionEnabled());
        map.put("compression_delay_hours", config.getCompressionDelayHours());
        map.put("compression_threshold", config.getCompressionThreshold());
        map.put("created_by", config.getCreatedBy());
        map.put("create_time", config.getCreateTime() != null ? 
                config.getCreateTime().format(DATE_TIME_FORMATTER) : null);
        return map;
    }
    
    /**
     * Map转换为数据生命周期配置
     * 
     * @param tableName 表名
     * @param configMap 配置Map
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig mapToDataLifecycleConfig(String tableName, Map<String, Object> configMap) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setTableName(tableName);
        
        if (configMap.containsKey("retention_days")) {
            config.setRetentionDays((Integer) configMap.get("retention_days"));
        }
        if (configMap.containsKey("auto_partition_enabled")) {
            config.setAutoPartitionEnabled((Boolean) configMap.get("auto_partition_enabled"));
        }
        if (configMap.containsKey("partition_column")) {
            config.setPartitionColumn((String) configMap.get("partition_column"));
        }
        if (configMap.containsKey("partition_granularity")) {
            String granularity = (String) configMap.get("partition_granularity");
            if (granularity != null) {
                config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.valueOf(granularity));
            }
        }
        if (configMap.containsKey("hot_partition_num")) {
            config.setHotPartitionNum((Integer) configMap.get("hot_partition_num"));
        }
        if (configMap.containsKey("warm_partition_num")) {
            config.setWarmPartitionNum((Integer) configMap.get("warm_partition_num"));
        }
        if (configMap.containsKey("cold_partition_num")) {
            config.setColdPartitionNum((Integer) configMap.get("cold_partition_num"));
        }
        if (configMap.containsKey("auto_cleanup_enabled")) {
            config.setAutoCleanupEnabled((Boolean) configMap.get("auto_cleanup_enabled"));
        }
        if (configMap.containsKey("compression_enabled")) {
            config.setCompressionEnabled((Boolean) configMap.get("compression_enabled"));
        }
        if (configMap.containsKey("compression_delay_hours")) {
            config.setCompressionDelayHours((Integer) configMap.get("compression_delay_hours"));
        }
        if (configMap.containsKey("compression_threshold")) {
            config.setCompressionThreshold((Double) configMap.get("compression_threshold"));
        }
        
        return config;
    }
    
    /**
     * Map转换为维护任务
     * 
     * @param taskConfig 任务配置Map
     * @return 维护任务
     */
    public MaintenanceTask mapToMaintenanceTask(Map<String, Object> taskConfig) {
        MaintenanceTask task = new MaintenanceTask();
        
        if (taskConfig.containsKey("task_name")) {
            task.setTaskName((String) taskConfig.get("task_name"));
        }
        if (taskConfig.containsKey("task_type")) {
            String taskType = (String) taskConfig.get("task_type");
            if (taskType != null) {
                task.setTaskType(MaintenanceTask.TaskType.valueOf(taskType));
            }
        }
        if (taskConfig.containsKey("priority")) {
            String priority = (String) taskConfig.get("priority");
            if (priority != null) {
                task.setPriority(MaintenanceTask.TaskPriority.valueOf(priority));
            }
        }
        if (taskConfig.containsKey("target_table")) {
            task.setTargetTable((String) taskConfig.get("target_table"));
        }
        if (taskConfig.containsKey("task_config")) {
            task.setTaskConfig((Map<String, Object>) taskConfig.get("task_config"));
        }
        
        // 设置默认状态
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        
        return task;
    }
}
