package com.geeksec.admin.domain.event;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 维护任务完成事件
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class MaintenanceTaskCompletedEvent extends DomainEvent {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 目标表名
     */
    private String targetTable;
    
    /**
     * 执行结果
     */
    private String result;
    
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行时长（毫秒）
     */
    private Long executionDurationMs;
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 构造函数
     */
    public MaintenanceTaskCompletedEvent() {
        super();
    }
    
    /**
     * 构造函数
     * 
     * @param taskId 任务ID
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param targetTable 目标表名
     * @param result 执行结果
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param success 是否成功
     * @param initiatedBy 事件发起者
     */
    public MaintenanceTaskCompletedEvent(String taskId, String taskName, String taskType, 
                                       String targetTable, String result, LocalDateTime startTime, 
                                       LocalDateTime endTime, Boolean success, String initiatedBy) {
        super(taskId, "MaintenanceTask", initiatedBy);
        this.taskId = taskId;
        this.taskName = taskName;
        this.taskType = taskType;
        this.targetTable = targetTable;
        this.result = result;
        this.startTime = startTime;
        this.endTime = endTime;
        this.success = success;
        
        if (startTime != null && endTime != null) {
            this.executionDurationMs = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }
}
