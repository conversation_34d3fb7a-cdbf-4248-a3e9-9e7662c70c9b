package com.geeksec.admin.domain.event;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 领域事件基础类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
public abstract class DomainEvent {
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 事件类型
     */
    private String eventType;
    
    /**
     * 聚合根ID
     */
    private String aggregateId;
    
    /**
     * 聚合根类型
     */
    private String aggregateType;
    
    /**
     * 事件发生时间
     */
    private LocalDateTime occurredOn;
    
    /**
     * 事件版本
     */
    private Integer version;
    
    /**
     * 事件发起者
     */
    private String initiatedBy;
    
    /**
     * 构造函数
     */
    protected DomainEvent() {
        this.eventId = UUID.randomUUID().toString();
        this.occurredOn = LocalDateTime.now();
        this.version = 1;
        this.eventType = this.getClass().getSimpleName();
    }
    
    /**
     * 构造函数
     * 
     * @param aggregateId 聚合根ID
     * @param aggregateType 聚合根类型
     * @param initiatedBy 事件发起者
     */
    protected DomainEvent(String aggregateId, String aggregateType, String initiatedBy) {
        this();
        this.aggregateId = aggregateId;
        this.aggregateType = aggregateType;
        this.initiatedBy = initiatedBy;
    }
}
