package com.geeksec.admin.domain.factory;

import com.geeksec.admin.domain.model.MaintenanceTask;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 维护任务工厂
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class MaintenanceTaskFactory {
    
    /**
     * 创建数据清理任务
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @param createdBy 创建者
     * @return 维护任务
     */
    public MaintenanceTask createDataCleanupTask(String tableName, Integer retentionDays, String createdBy) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskName("数据清理任务 - " + tableName);
        task.setTaskType(MaintenanceTask.TaskType.DATA_CLEANUP);
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        task.setPriority(MaintenanceTask.TaskPriority.NORMAL);
        task.setTargetTable(tableName);
        task.setProgress(0);
        task.setCreateTime(LocalDateTime.now());
        task.setCreatedBy(createdBy);
        
        // 设置任务配置
        Map<String, Object> config = Map.of(
                "retention_days", retentionDays,
                "cleanup_type", "partition_based",
                "batch_size", 1000
        );
        task.setTaskConfig(config);
        
        return task;
    }
    
    /**
     * 创建数据压缩任务
     * 
     * @param tableName 表名
     * @param partitionName 分区名（可选）
     * @param compressionThreshold 压缩阈值
     * @param createdBy 创建者
     * @return 维护任务
     */
    public MaintenanceTask createDataCompressionTask(String tableName, String partitionName, 
                                                   Double compressionThreshold, String createdBy) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskName("数据压缩任务 - " + tableName + 
                (partitionName != null ? " (" + partitionName + ")" : ""));
        task.setTaskType(MaintenanceTask.TaskType.DATA_COMPRESSION);
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        task.setPriority(MaintenanceTask.TaskPriority.LOW);
        task.setTargetTable(tableName);
        task.setProgress(0);
        task.setCreateTime(LocalDateTime.now());
        task.setCreatedBy(createdBy);
        
        // 设置任务配置
        Map<String, Object> config = Map.of(
                "compression_threshold", compressionThreshold,
                "partition_name", partitionName != null ? partitionName : "",
                "compression_algorithm", "lz4"
        );
        task.setTaskConfig(config);
        
        return task;
    }
    
    /**
     * 创建统计信息刷新任务
     * 
     * @param tableName 表名
     * @param createdBy 创建者
     * @return 维护任务
     */
    public MaintenanceTask createStatisticsRefreshTask(String tableName, String createdBy) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskName("统计信息刷新任务 - " + tableName);
        task.setTaskType(MaintenanceTask.TaskType.STATISTICS_REFRESH);
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        task.setPriority(MaintenanceTask.TaskPriority.HIGH);
        task.setTargetTable(tableName);
        task.setProgress(0);
        task.setCreateTime(LocalDateTime.now());
        task.setCreatedBy(createdBy);
        
        // 设置任务配置
        Map<String, Object> config = Map.of(
                "refresh_type", "full",
                "include_indexes", true
        );
        task.setTaskConfig(config);
        
        return task;
    }
    
    /**
     * 创建数据完整性检查任务
     * 
     * @param tableName 表名
     * @param checkType 检查类型
     * @param createdBy 创建者
     * @return 维护任务
     */
    public MaintenanceTask createDataIntegrityCheckTask(String tableName, String checkType, String createdBy) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskName("数据完整性检查任务 - " + tableName);
        task.setTaskType(MaintenanceTask.TaskType.DATA_INTEGRITY_CHECK);
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        task.setPriority(MaintenanceTask.TaskPriority.URGENT);
        task.setTargetTable(tableName);
        task.setProgress(0);
        task.setCreateTime(LocalDateTime.now());
        task.setCreatedBy(createdBy);
        
        // 设置任务配置
        Map<String, Object> config = Map.of(
                "check_type", checkType,
                "repair_mode", false,
                "timeout_minutes", 60
        );
        task.setTaskConfig(config);
        
        return task;
    }
    
    /**
     * 创建自定义维护任务
     * 
     * @param taskName 任务名称
     * @param taskType 任务类型
     * @param priority 优先级
     * @param targetTable 目标表
     * @param taskConfig 任务配置
     * @param createdBy 创建者
     * @return 维护任务
     */
    public MaintenanceTask createCustomTask(String taskName, MaintenanceTask.TaskType taskType,
                                          MaintenanceTask.TaskPriority priority, String targetTable,
                                          Map<String, Object> taskConfig, String createdBy) {
        MaintenanceTask task = new MaintenanceTask();
        task.setTaskId(UUID.randomUUID().toString());
        task.setTaskName(taskName);
        task.setTaskType(taskType);
        task.setStatus(MaintenanceTask.TaskStatus.PENDING);
        task.setPriority(priority);
        task.setTargetTable(targetTable);
        task.setTaskConfig(taskConfig);
        task.setProgress(0);
        task.setCreateTime(LocalDateTime.now());
        task.setCreatedBy(createdBy);
        
        return task;
    }
}
