package com.geeksec.admin.infrastructure.persistence.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 维护任务数据对象
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@Accessors(chain = true)
@Table("maintenance_task")
public class MaintenanceTaskDO {
    
    /**
     * 任务ID
     */
    @Id(keyType = KeyType.Generator, value = "uuid")
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务类型
     */
    private String taskType;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 任务优先级
     */
    private String priority;
    
    /**
     * 目标表名
     */
    private String targetTable;
    
    /**
     * 任务配置（JSON格式）
     */
    private String taskConfig;
    
    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;
    
    /**
     * 实际开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 实际结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行进度（0-100）
     */
    private Integer progress;
    
    /**
     * 执行结果
     */
    private String result;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新者
     */
    private String updatedBy;
}
