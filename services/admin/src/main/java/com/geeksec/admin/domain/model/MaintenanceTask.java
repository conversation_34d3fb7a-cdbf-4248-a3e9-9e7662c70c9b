package com.geeksec.admin.domain.model;

import com.geeksec.admin.domain.event.MaintenanceTaskCompletedEvent;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 维护任务聚合根
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MaintenanceTask extends AggregateRoot<String> {
    
    /**
     * 任务ID
     */
    private String taskId;
    
    /**
     * 任务名称
     */
    private String taskName;
    
    /**
     * 任务类型
     */
    private TaskType taskType;
    
    /**
     * 任务状态
     */
    private TaskStatus status;
    
    /**
     * 任务优先级
     */
    private TaskPriority priority;
    
    /**
     * 目标表名
     */
    private String targetTable;
    
    /**
     * 任务配置
     */
    private Map<String, Object> taskConfig;
    
    /**
     * 计划执行时间
     */
    private LocalDateTime scheduledTime;
    
    /**
     * 实际开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 实际结束时间
     */
    private LocalDateTime endTime;
    
    /**
     * 执行进度（0-100）
     */
    private Integer progress;
    
    /**
     * 执行结果
     */
    private String result;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 创建者
     */
    private String createdBy;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 更新者
     */
    private String updatedBy;
    
    /**
     * 任务类型枚举
     */
    public enum TaskType {
        DATA_CLEANUP("数据清理"),
        DATA_BACKUP("数据备份"),
        DATA_RESTORE("数据恢复"),
        DATA_COMPACTION("数据压缩"),
        PARTITION_MANAGEMENT("分区管理"),
        STATISTICS_REFRESH("统计信息刷新"),
        HEALTH_CHECK("健康检查");
        
        private final String description;
        
        TaskType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待执行"),
        RUNNING("执行中"),
        COMPLETED("已完成"),
        FAILED("执行失败"),
        CANCELLED("已取消"),
        PAUSED("已暂停");
        
        private final String description;
        
        TaskStatus(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 任务优先级枚举
     */
    public enum TaskPriority {
        LOW("低"),
        NORMAL("普通"),
        HIGH("高"),
        URGENT("紧急");
        
        private final String description;
        
        TaskPriority(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    /**
     * 开始执行任务
     * 
     * @param executor 执行者
     */
    public void start(String executor) {
        this.status = TaskStatus.RUNNING;
        this.startTime = LocalDateTime.now();
        this.progress = 0;
        this.updatedBy = executor;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 完成任务
     *
     * @param result 执行结果
     * @param executor 执行者
     */
    public void complete(String result, String executor) {
        this.status = TaskStatus.COMPLETED;
        this.endTime = LocalDateTime.now();
        this.progress = 100;
        this.result = result;
        this.updatedBy = executor;
        this.updateTime = LocalDateTime.now();

        // 发布任务完成事件
        MaintenanceTaskCompletedEvent event = new MaintenanceTaskCompletedEvent(
                this.taskId, this.taskName, this.taskType.name(), this.targetTable,
                result, this.startTime, this.endTime, true, executor);
        this.addDomainEvent(event);
    }
    
    /**
     * 任务失败
     *
     * @param errorMessage 错误信息
     * @param executor 执行者
     */
    public void fail(String errorMessage, String executor) {
        this.status = TaskStatus.FAILED;
        this.endTime = LocalDateTime.now();
        this.errorMessage = errorMessage;
        this.updatedBy = executor;
        this.updateTime = LocalDateTime.now();

        // 发布任务完成事件（失败）
        MaintenanceTaskCompletedEvent event = new MaintenanceTaskCompletedEvent(
                this.taskId, this.taskName, this.taskType.name(), this.targetTable,
                errorMessage, this.startTime, this.endTime, false, executor);
        this.addDomainEvent(event);
    }
    
    /**
     * 取消任务
     * 
     * @param executor 执行者
     */
    public void cancel(String executor) {
        this.status = TaskStatus.CANCELLED;
        this.endTime = LocalDateTime.now();
        this.updatedBy = executor;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 更新进度
     * 
     * @param progress 进度值
     * @param executor 执行者
     */
    public void updateProgress(Integer progress, String executor) {
        this.progress = progress;
        this.updatedBy = executor;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 检查任务是否可以执行
     * 
     * @return 是否可以执行
     */
    public boolean canExecute() {
        return status == TaskStatus.PENDING || status == TaskStatus.PAUSED;
    }
    
    /**
     * 检查任务是否正在执行
     * 
     * @return 是否正在执行
     */
    public boolean isRunning() {
        return status == TaskStatus.RUNNING;
    }
    
    /**
     * 检查任务是否已完成
     * 
     * @return 是否已完成
     */
    public boolean isCompleted() {
        return status == TaskStatus.COMPLETED || status == TaskStatus.FAILED || status == TaskStatus.CANCELLED;
    }
    
    /**
     * 获取执行时长（分钟）
     *
     * @return 执行时长
     */
    public Long getExecutionDurationMinutes() {
        if (startTime == null) {
            return null;
        }
        LocalDateTime end = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, end).toMinutes();
    }

    @Override
    public String getId() {
        return this.taskId;
    }
}
