package com.geeksec.admin.infrastructure.persistence;

import com.geeksec.admin.domain.model.DataLifecycleConfig;
import com.geeksec.admin.domain.repository.DataLifecycleConfigRepository;
import com.geeksec.admin.infrastructure.persistence.entity.DataLifecycleConfigDO;
import com.geeksec.admin.infrastructure.persistence.mapper.DataLifecycleConfigMapper;
import com.geeksec.admin.infrastructure.persistence.converter.DomainModelConverter;
import com.mybatisflex.core.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 数据生命周期配置仓储实现类
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Repository
@RequiredArgsConstructor
public class DataLifecycleConfigRepositoryImpl implements DataLifecycleConfigRepository {

    private final DataLifecycleConfigMapper mapper;
    private final DomainModelConverter converter;
    
    @Override
    public Optional<DataLifecycleConfig> findById(String configId) {
        log.debug("根据ID查找配置: {}", configId);
        DataLifecycleConfigDO configDO = mapper.selectOneById(configId);
        return Optional.ofNullable(converter.toDomainModel(configDO));
    }

    @Override
    public Optional<DataLifecycleConfig> findByTableName(String tableName) {
        log.debug("根据表名查找配置: {}", tableName);
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("table_name = ?", tableName);
        DataLifecycleConfigDO configDO = mapper.selectOneByQuery(queryWrapper);
        return Optional.ofNullable(converter.toDomainModel(configDO));
    }

    @Override
    public List<DataLifecycleConfig> findAll() {
        log.debug("查找所有配置");
        List<DataLifecycleConfigDO> configDoList = mapper.selectAll();
        return configDoList.stream()
                .map(converter::toDomainModel)
                .toList();
    }
    
    @Override
    public List<DataLifecycleConfig> findByAutoCleanupEnabled() {
        log.debug("查找启用自动清理的配置");
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("auto_cleanup_enabled = ?", true);
        List<DataLifecycleConfigDO> configDoList = mapper.selectListByQuery(queryWrapper);
        return configDoList.stream()
                .map(converter::toDomainModel)
                .toList();
    }

    @Override
    public List<DataLifecycleConfig> findByCompressionEnabled() {
        log.debug("查找启用压缩的配置");
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("compression_enabled = ?", true);
        List<DataLifecycleConfigDO> configDoList = mapper.selectListByQuery(queryWrapper);
        return configDoList.stream()
                .map(converter::toDomainModel)
                .toList();
    }
    
    @Override
    public DataLifecycleConfig save(DataLifecycleConfig config) {
        log.debug("保存配置: {}", config.getTableName());

        if (config.getConfigId() == null) {
            // 新增
            config.setConfigId(UUID.randomUUID().toString());
            config.setCreateTime(LocalDateTime.now());
        }

        config.setUpdateTime(LocalDateTime.now());
        DataLifecycleConfigDO configDO = converter.toDataObject(config);

        if (existsById(config.getConfigId())) {
            mapper.update(configDO);
        } else {
            mapper.insert(configDO);
        }

        return config;
    }

    @Override
    public void deleteById(String configId) {
        log.debug("删除配置: {}", configId);
        mapper.deleteById(configId);
    }

    @Override
    public void deleteByTableName(String tableName) {
        log.debug("根据表名删除配置: {}", tableName);
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("table_name = ?", tableName);
        mapper.deleteByQuery(queryWrapper);
    }

    @Override
    public boolean existsById(String configId) {
        return mapper.selectOneById(configId) != null;
    }

    @Override
    public boolean existsByTableName(String tableName) {
        QueryWrapper queryWrapper = QueryWrapper.create()
                .where("table_name = ?", tableName);
        return mapper.selectOneByQuery(queryWrapper) != null;
    }
    
    /**
     * 初始化默认数据
     */
    public void initDefaultData() {
        log.info("初始化默认数据生命周期配置");

        // 检查是否已经初始化过
        if (existsByTableName("dwd_session_logs")) {
            log.info("默认配置已存在，跳过初始化");
            return;
        }

        // 会话日志表配置
        DataLifecycleConfig sessionLogConfig = new DataLifecycleConfig()
                .setTableName("dwd_session_logs")
                .setRetentionDays(90)
                .setAutoPartitionEnabled(true)
                .setPartitionColumn("session_start_time")
                .setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY)
                .setHotPartitionNum(7)
                .setWarmPartitionNum(30)
                .setColdPartitionNum(53)
                .setAutoCleanupEnabled(true)
                .setCompressionEnabled(true)
                .setCompressionDelayHours(24)
                .setCompressionThreshold(0.8)
                .setCreatedBy("system")
                .setUpdatedBy("system");

        save(sessionLogConfig);

        // 其他表配置
        DataLifecycleConfig alertLogConfig = new DataLifecycleConfig()
                .setTableName("dwd_alert_logs")
                .setRetentionDays(180)
                .setAutoPartitionEnabled(true)
                .setPartitionColumn("alert_time")
                .setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY)
                .setHotPartitionNum(7)
                .setWarmPartitionNum(60)
                .setColdPartitionNum(113)
                .setAutoCleanupEnabled(true)
                .setCompressionEnabled(true)
                .setCompressionDelayHours(48)
                .setCompressionThreshold(0.75)
                .setCreatedBy("system")
                .setUpdatedBy("system");

        save(alertLogConfig);

        log.info("默认数据生命周期配置初始化完成");
    }
}
