package com.geeksec.admin.infrastructure.event;

import com.geeksec.admin.domain.event.DomainEvent;
import com.geeksec.admin.domain.event.DomainEventPublisher;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Spring 领域事件发布器实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SpringDomainEventPublisher implements DomainEventPublisher {
    
    private final ApplicationEventPublisher applicationEventPublisher;
    
    @Override
    public void publish(DomainEvent event) {
        log.debug("发布领域事件: {}, 聚合根ID: {}", event.getEventType(), event.getAggregateId());
        applicationEventPublisher.publishEvent(event);
    }
    
    @Override
    @Async
    public void publishAsync(DomainEvent event) {
        log.debug("异步发布领域事件: {}, 聚合根ID: {}", event.getEventType(), event.getAggregateId());
        applicationEventPublisher.publishEvent(event);
    }
}
