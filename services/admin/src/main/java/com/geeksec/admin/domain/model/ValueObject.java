package com.geeksec.admin.domain.model;

/**
 * 值对象标识接口
 * 
 * 值对象特征：
 * 1. 不可变性：一旦创建，其状态不能改变
 * 2. 相等性：基于属性值而非身份进行比较
 * 3. 无身份：没有唯一标识符
 * 4. 可替换性：具有相同属性值的值对象可以相互替换
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface ValueObject {
    
    /**
     * 验证值对象的有效性
     * 
     * @return 验证结果
     */
    default ValidationResult validate() {
        return new ValidationResult(); // 默认返回有效结果
    }
}
