package com.geeksec.admin.domain.factory;

import com.geeksec.admin.domain.model.DataLifecycleConfig;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 数据生命周期配置工厂
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Component
public class DataLifecycleConfigFactory {
    
    /**
     * 创建默认的数据生命周期配置
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createDefaultConfig(String tableName, Integer retentionDays, String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName(tableName);
        config.setRetentionDays(retentionDays);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn("create_time");
        config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY);
        config.setHotPartitionNum(7);
        config.setWarmPartitionNum(30);
        config.setColdPartitionNum(retentionDays - 37);
        config.setAutoCleanupEnabled(true);
        config.setCompressionEnabled(true);
        config.setCompressionDelayHours(24);
        config.setCompressionThreshold(0.8);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        return config;
    }
    
    /**
     * 创建会话日志表的配置
     * 
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createSessionLogConfig(String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName("dwd_session_logs");
        config.setRetentionDays(90);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn("session_start_time");
        config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY);
        config.setHotPartitionNum(7);
        config.setWarmPartitionNum(30);
        config.setColdPartitionNum(53);
        config.setAutoCleanupEnabled(true);
        config.setCompressionEnabled(true);
        config.setCompressionDelayHours(24);
        config.setCompressionThreshold(0.8);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        return config;
    }
    
    /**
     * 创建告警日志表的配置
     * 
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createAlertLogConfig(String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName("dwd_alert_logs");
        config.setRetentionDays(180);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn("alert_time");
        config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.DAY);
        config.setHotPartitionNum(7);
        config.setWarmPartitionNum(60);
        config.setColdPartitionNum(113);
        config.setAutoCleanupEnabled(true);
        config.setCompressionEnabled(true);
        config.setCompressionDelayHours(48);
        config.setCompressionThreshold(0.75);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        return config;
    }
    
    /**
     * 创建高频数据表的配置（短保留期）
     * 
     * @param tableName 表名
     * @param partitionColumn 分区列
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createHighFrequencyConfig(String tableName, String partitionColumn, String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName(tableName);
        config.setRetentionDays(30);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn(partitionColumn);
        config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.HOUR);
        config.setHotPartitionNum(24);
        config.setWarmPartitionNum(168); // 7天
        config.setColdPartitionNum(552); // 23天
        config.setAutoCleanupEnabled(true);
        config.setCompressionEnabled(true);
        config.setCompressionDelayHours(6);
        config.setCompressionThreshold(0.9);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        return config;
    }
    
    /**
     * 创建归档数据表的配置（长保留期）
     * 
     * @param tableName 表名
     * @param partitionColumn 分区列
     * @param retentionDays 保留天数
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createArchiveConfig(String tableName, String partitionColumn, 
                                                 Integer retentionDays, String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName(tableName);
        config.setRetentionDays(retentionDays);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn(partitionColumn);
        config.setPartitionGranularity(DataLifecycleConfig.PartitionGranularity.MONTH);
        config.setHotPartitionNum(1);
        config.setWarmPartitionNum(3);
        config.setColdPartitionNum(retentionDays / 30 - 4);
        config.setAutoCleanupEnabled(true);
        config.setCompressionEnabled(true);
        config.setCompressionDelayHours(72);
        config.setCompressionThreshold(0.6);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        return config;
    }
    
    /**
     * 创建自定义配置
     * 
     * @param tableName 表名
     * @param retentionDays 保留天数
     * @param partitionColumn 分区列
     * @param granularity 分区粒度
     * @param autoCleanupEnabled 是否启用自动清理
     * @param compressionEnabled 是否启用压缩
     * @param createdBy 创建者
     * @return 数据生命周期配置
     */
    public DataLifecycleConfig createCustomConfig(String tableName, Integer retentionDays,
                                                String partitionColumn, DataLifecycleConfig.PartitionGranularity granularity,
                                                Boolean autoCleanupEnabled, Boolean compressionEnabled,
                                                String createdBy) {
        DataLifecycleConfig config = new DataLifecycleConfig();
        config.setConfigId(UUID.randomUUID().toString());
        config.setTableName(tableName);
        config.setRetentionDays(retentionDays);
        config.setAutoPartitionEnabled(true);
        config.setPartitionColumn(partitionColumn);
        config.setPartitionGranularity(granularity);
        config.setAutoCleanupEnabled(autoCleanupEnabled);
        config.setCompressionEnabled(compressionEnabled);
        config.setCreateTime(LocalDateTime.now());
        config.setCreatedBy(createdBy);
        
        // 根据粒度设置默认分区数量
        switch (granularity) {
            case HOUR -> {
                config.setHotPartitionNum(24);
                config.setWarmPartitionNum(168);
                config.setColdPartitionNum(retentionDays * 24 - 192);
            }
            case DAY -> {
                config.setHotPartitionNum(7);
                config.setWarmPartitionNum(30);
                config.setColdPartitionNum(retentionDays - 37);
            }
            case WEEK -> {
                config.setHotPartitionNum(2);
                config.setWarmPartitionNum(8);
                config.setColdPartitionNum(retentionDays / 7 - 10);
            }
            case MONTH -> {
                config.setHotPartitionNum(1);
                config.setWarmPartitionNum(3);
                config.setColdPartitionNum(retentionDays / 30 - 4);
            }
        }
        
        // 设置压缩相关参数
        if (compressionEnabled) {
            config.setCompressionDelayHours(granularity == DataLifecycleConfig.PartitionGranularity.HOUR ? 6 : 24);
            config.setCompressionThreshold(0.8);
        }
        
        return config;
    }
}
