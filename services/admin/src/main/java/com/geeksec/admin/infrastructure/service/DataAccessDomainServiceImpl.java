package com.geeksec.admin.infrastructure.service;

import com.geeksec.admin.domain.service.DataAccessDomainService;
import com.geeksec.admin.domain.model.PartitionInfo;
import com.geeksec.admin.domain.model.StorageStats;
import com.geeksec.admin.infrastructure.external.DorisDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 数据访问领域服务实现 - 基础设施层实现
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataAccessDomainServiceImpl implements DataAccessDomainService {
    
    private final DorisDataService dorisDataService;
    
    @Override
    public List<PartitionInfo> getTablePartitions(String tableName) {
        log.debug("获取表分区信息: {}", tableName);
        return dorisDataService.getTablePartitions(tableName);
    }
    
    @Override
    public StorageStats getStorageStats() {
        log.debug("获取存储统计信息");
        return dorisDataService.getStorageStats();
    }
    
    @Override
    public List<StorageStats> getDataGrowthTrend(String tableName, int days) {
        log.debug("获取数据增长趋势: {}, 天数: {}", tableName, days);
        return dorisDataService.getDataGrowthTrend(tableName, days);
    }
    
    @Override
    public int cleanupPartitions(String tableName, List<String> partitionNames) {
        log.debug("清理分区: {}, 分区数量: {}", tableName, partitionNames.size());
        return dorisDataService.cleanupPartitions(tableName, partitionNames);
    }
    
    @Override
    public boolean refreshTableStatistics(String tableName) {
        log.debug("刷新表统计信息: {}", tableName);
        return dorisDataService.refreshTableStatistics(tableName);
    }
    
    @Override
    public boolean checkDataIntegrity(String tableName, String checkType) {
        log.debug("检查数据完整性: {}, 检查类型: {}", tableName, checkType);
        return dorisDataService.checkDataIntegrity(tableName, checkType);
    }
    
    @Override
    public StorageStats getTableStorageStats(String tableName) {
        log.debug("获取表存储统计信息: {}", tableName);
        return dorisDataService.getTableStorageStats(tableName);
    }
    
    @Override
    public boolean executeCompaction(String tableName, String partitionName) {
        log.debug("执行数据压缩: {}, 分区: {}", tableName, partitionName);
        return dorisDataService.executeCompaction(tableName, partitionName);
    }
}
