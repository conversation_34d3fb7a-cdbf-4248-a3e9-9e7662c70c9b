package com.geeksec.admin.domain.service;

import com.geeksec.admin.domain.model.PartitionInfo;
import com.geeksec.admin.domain.model.StorageStats;

import java.util.List;

/**
 * 数据访问领域服务接口 - 抽象数据访问操作
 * 
 * <AUTHOR> Team
 * @since 3.0.0
 */
public interface DataAccessDomainService {
    
    /**
     * 获取表分区信息
     * 
     * @param tableName 表名
     * @return 分区信息列表
     */
    List<PartitionInfo> getTablePartitions(String tableName);
    
    /**
     * 获取存储统计信息
     * 
     * @return 存储统计信息
     */
    StorageStats getStorageStats();
    
    /**
     * 获取数据增长趋势
     * 
     * @param tableName 表名
     * @param days 统计天数
     * @return 增长趋势数据
     */
    List<StorageStats> getDataGrowthTrend(String tableName, int days);
    
    /**
     * 清理分区
     * 
     * @param tableName 表名
     * @param partitionNames 分区名称列表
     * @return 清理的分区数量
     */
    int cleanupPartitions(String tableName, List<String> partitionNames);
    
    /**
     * 刷新表统计信息
     * 
     * @param tableName 表名
     * @return 是否成功
     */
    boolean refreshTableStatistics(String tableName);
    
    /**
     * 检查数据完整性
     *
     * @param tableName 表名
     * @param checkType 检查类型
     * @return 是否通过检查
     */
    boolean checkDataIntegrity(String tableName, String checkType);

    /**
     * 获取表存储统计信息
     *
     * @param tableName 表名
     * @return 表存储统计信息
     */
    StorageStats getTableStorageStats(String tableName);

    /**
     * 执行数据压缩
     *
     * @param tableName 表名
     * @param partitionName 分区名（可选）
     * @return 是否成功
     */
    boolean executeCompaction(String tableName, String partitionName);
}
