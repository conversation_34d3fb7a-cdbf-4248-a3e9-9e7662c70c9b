package com.geeksec.session.domain.entities;

import com.geeksec.session.domain.valueobjects.NetworkEndpoint;
import com.geeksec.session.domain.valueobjects.SessionStatistics;
import com.geeksec.session.domain.valueobjects.SessionFingerprint;
import com.geeksec.session.domain.valueobjects.ProtocolMetadata;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 会话聚合根 - 网络会话的核心领域实体
 * 
 * 聚合根负责维护会话的一致性边界，包含核心业务规则和行为
 *
 * <AUTHOR> Team
 * @since 3.0.0
 */
@Getter
@Setter
@Table("dwd_session_logs")
public class Session {

    // ==================== 聚合根标识 ====================
    
    /**
     * 会话唯一标识符
     */
    @Id
    private String sessionId;

    // ==================== 核心会话信息 ====================
    
    /**
     * 源网络端点
     */
    private NetworkEndpoint sourceEndpoint;
    
    /**
     * 目标网络端点
     */
    private NetworkEndpoint destinationEndpoint;
    
    /**
     * IP协议号
     */
    private Integer protocol;
    
    /**
     * 会话开始时间
     */
    private LocalDateTime sessionStartTime;
    
    /**
     * 会话结束时间
     */
    private LocalDateTime sessionEndTime;
    
    /**
     * 应用ID
     */
    private Integer appId;
    
    /**
     * 应用名称
     */
    private String appName;
    
    /**
     * 服务器IP地址
     */
    private String serverIp;
    
    /**
     * 任务ID
     */
    private Integer taskId;
    
    /**
     * 批次ID
     */
    private Integer batchId;
    
    /**
     * 线程ID
     */
    private Integer threadId;

    // ==================== 组合的值对象 ====================
    
    /**
     * 会话统计信息
     */
    private SessionStatistics statistics;
    
    /**
     * 协议指纹信息
     */
    private SessionFingerprint fingerprint;
    
    /**
     * 协议元数据
     */
    private ProtocolMetadata protocolMetadata;
    
    /**
     * 会话标签ID列表
     */
    private List<Integer> labels = new ArrayList<>();
    
    /**
     * 规则标签列表
     */
    private List<Integer> ruleLabels = new ArrayList<>();
    
    /**
     * 扩展数据
     */
    private Map<String, Object> extensionData;

    // ==================== 系统字段 ====================
    
    /**
     * 记录创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 记录更新时间
     */
    private LocalDateTime updateTime;

    // ==================== 领域行为方法 ====================
    
    /**
     * 判断会话是否处于活跃状态
     * 
     * @return 如果会话仍在进行中返回true
     */
    public boolean isActive() {
        return sessionEndTime == null || sessionEndTime.isAfter(LocalDateTime.now());
    }
    
    /**
     * 计算会话持续时间（秒）
     * 
     * @return 会话持续时间，如果会话未结束则返回到当前时间的持续时间
     */
    public long getDurationInSeconds() {
        LocalDateTime endTime = sessionEndTime != null ? sessionEndTime : LocalDateTime.now();
        return ChronoUnit.SECONDS.between(sessionStartTime, endTime);
    }
    
    /**
     * 获取会话的网络方向
     * 
     * @return 网络方向描述
     */
    public String getNetworkDirection() {
        if (sourceEndpoint.isInternal() && destinationEndpoint.isInternal()) {
            return "内网到内网";
        } else if (sourceEndpoint.isInternal() && !destinationEndpoint.isInternal()) {
            return "内网到外网";
        } else if (!sourceEndpoint.isInternal() && destinationEndpoint.isInternal()) {
            return "外网到内网";
        } else {
            return "外网到外网";
        }
    }
    
    /**
     * 判断是否为可疑会话
     * 基于规则级别和统计信息进行判断
     * 
     * @return 如果是可疑会话返回true
     */
    public boolean isSuspicious() {
        // 基于业务规则判断可疑性
        if (statistics != null && statistics.getRuleLevel() != null && statistics.getRuleLevel() > 3) {
            return true;
        }
        
        // 可以添加更多业务规则
        return false;
    }
    
    // ==================== 手动添加的 Getter 方法 ====================
    
    /**
     * 获取会话统计信息
     * 
     * @return 会话统计信息
     */
    public SessionStatistics getStatistics() {
        return statistics;
    }
    
    /**
     * 获取协议指纹信息
     * 
     * @return 协议指纹信息
     */
    public SessionFingerprint getFingerprint() {
        return fingerprint;
    }
    
    /**
     * 获取协议元数据
     * 
     * @return 协议元数据
     */
    public ProtocolMetadata getProtocolMetadata() {
        return protocolMetadata;
    }
    
    /**
     * 获取会话的主要协议
     * 
     * @return 主要协议名称
     */
    public String getPrimaryProtocol() {
        if (appName != null && !appName.trim().isEmpty()) {
            return appName;
        }
        return "Protocol-" + protocol;
    }
    
    /**
     * 验证会话数据的完整性
     * 
     * @return 如果数据完整返回true
     */
    public boolean isDataComplete() {
        return sessionId != null && 
               sourceEndpoint != null && 
               destinationEndpoint != null && 
               sessionStartTime != null;
    }
    
    /**
     * 更新会话结束时间
     * 
     * @param endTime 结束时间
     */
    public void endSession(LocalDateTime endTime) {
        if (endTime.isBefore(sessionStartTime)) {
            throw new IllegalArgumentException("会话结束时间不能早于开始时间");
        }
        this.sessionEndTime = endTime;
        this.updateTime = LocalDateTime.now();
    }
    
    /**
     * 添加标签
     * 
     * @param labelId 标签ID
     */
    public void addLabel(Integer labelId) {
        if (labelId != null && !labels.contains(labelId)) {
            labels.add(labelId);
            this.updateTime = LocalDateTime.now();
        }
    }
    
    /**
     * 移除标签
     * 
     * @param labelId 标签ID
     */
    public void removeLabel(Integer labelId) {
        if (labels.remove(labelId)) {
            this.updateTime = LocalDateTime.now();
        }
    }
}