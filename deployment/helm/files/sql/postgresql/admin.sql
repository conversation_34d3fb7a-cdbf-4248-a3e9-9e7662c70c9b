-- ========================================
-- NTA 3.0 系统管理模块数据库初始化脚本
-- ========================================
--
-- 本文件包含系统管理相关的所有表结构定义
-- 包括：数据生命周期管理、维护任务、用户配置、系统配置等
--
-- ========================================

-- ========================================
-- 数据生命周期管理相关表
-- ========================================

-- 数据生命周期配置表
DROP TABLE IF EXISTS data_lifecycle_config CASCADE;

CREATE TABLE data_lifecycle_config (
    config_id VARCHAR(36) PRIMARY KEY,
    table_name VARCHAR(100) NOT NULL UNIQUE,
    retention_days INTEGER NOT NULL DEFAULT 90,
    auto_partition_enabled BOOLEAN DEFAULT TRUE,
    partition_column VARCHAR(50),
    partition_granularity VARCHAR(20) DEFAULT 'DAY',
    hot_partition_num INTEGER DEFAULT 7,
    warm_partition_num INTEGER DEFAULT 30,
    cold_partition_num INTEGER DEFAULT 53,
    auto_cleanup_enabled BOOLEAN DEFAULT TRUE,
    compression_enabled BOOLEAN DEFAULT TRUE,
    compression_delay_hours INTEGER DEFAULT 24,
    compression_threshold DECIMAL(3,2) DEFAULT 0.8,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    updated_by VARCHAR(50) DEFAULT 'system'
);

COMMENT ON TABLE data_lifecycle_config IS '数据生命周期配置表';
COMMENT ON COLUMN data_lifecycle_config.config_id IS '配置ID';
COMMENT ON COLUMN data_lifecycle_config.table_name IS '表名';
COMMENT ON COLUMN data_lifecycle_config.retention_days IS '数据保留天数';
COMMENT ON COLUMN data_lifecycle_config.auto_partition_enabled IS '是否启用自动分区';
COMMENT ON COLUMN data_lifecycle_config.partition_column IS '分区列名';
COMMENT ON COLUMN data_lifecycle_config.partition_granularity IS '分区粒度(HOUR/DAY/WEEK/MONTH)';

-- 维护任务表
DROP TABLE IF EXISTS maintenance_task CASCADE;

CREATE TABLE maintenance_task (
    task_id VARCHAR(36) PRIMARY KEY,
    task_name VARCHAR(200) NOT NULL,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'PENDING',
    priority VARCHAR(20) NOT NULL DEFAULT 'NORMAL',
    target_table VARCHAR(100),
    task_config TEXT,
    scheduled_time TIMESTAMP,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    progress INTEGER DEFAULT 0,
    result TEXT,
    error_message TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(50) DEFAULT 'system',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(50) DEFAULT 'system'
);

COMMENT ON TABLE maintenance_task IS '维护任务表';
COMMENT ON COLUMN maintenance_task.task_id IS '任务ID';
COMMENT ON COLUMN maintenance_task.task_name IS '任务名称';
COMMENT ON COLUMN maintenance_task.task_type IS '任务类型';
COMMENT ON COLUMN maintenance_task.status IS '任务状态(PENDING/RUNNING/COMPLETED/FAILED/CANCELLED)';
COMMENT ON COLUMN maintenance_task.priority IS '任务优先级(LOW/NORMAL/HIGH/URGENT)';
COMMENT ON COLUMN maintenance_task.target_table IS '目标表名';
COMMENT ON COLUMN maintenance_task.task_config IS '任务配置(JSON格式)';

-- ========================================
-- 系统配置管理相关表
-- ========================================

-- 系统状态配置表
DROP TABLE IF EXISTS system_state_config CASCADE;

CREATE TABLE system_state_config (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(255) NOT NULL,
    type_value TEXT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_state_config IS '系统状态配置表';
COMMENT ON COLUMN system_state_config.type_name IS '功能状态简写';
COMMENT ON COLUMN system_state_config.type_value IS '功能状态详细说明';
COMMENT ON COLUMN system_state_config.field_name IS '字段名称';

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;

CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    valset_id VARCHAR(255),
    val_id VARCHAR(255),
    value VARCHAR(255)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';

-- 硬盘模式配置表
DROP TABLE IF EXISTS disk_mode_config CASCADE;

CREATE TABLE disk_mode_config (
    id SERIAL PRIMARY KEY,
    field INTEGER
);

COMMENT ON TABLE disk_mode_config IS '硬盘模式配置表';
COMMENT ON COLUMN disk_mode_config.id IS '唯一识别ID';
COMMENT ON COLUMN disk_mode_config.field IS '硬盘模式（读盘模式&换盘模式）';

-- ========================================
-- 用户配置管理相关表
-- ========================================

-- 用户查询历史表
DROP TABLE IF EXISTS user_query_history CASCADE;

CREATE TABLE user_query_history (
    id SERIAL PRIMARY KEY,
    created_by INTEGER,
    user_name VARCHAR(128) NOT NULL,
    condition_text TEXT,
    query_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_history IS '用户查询历史表';
COMMENT ON COLUMN user_query_history.created_by IS '创建者用户ID';
COMMENT ON COLUMN user_query_history.user_name IS '查询用户名称';
COMMENT ON COLUMN user_query_history.condition_text IS '查询条件';

-- 用户查询模板表
DROP TABLE IF EXISTS user_query_template CASCADE;

CREATE TABLE user_query_template (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    user_name VARCHAR(32) NOT NULL,
    template_text TEXT,
    template_name VARCHAR(128),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE user_query_template IS '用户查询模板表';

-- 用户显示偏好配置表 (通用配置表，支持多模块)
DROP TABLE IF EXISTS user_display_preferences CASCADE;

CREATE TABLE user_display_preferences (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL,
    module VARCHAR(50) NOT NULL, -- 模块名：'certificate', 'session', 'alert', 'traffic' 等
    config_type VARCHAR(50) NOT NULL, -- 配置类型：'table_view', 'chart_view', 'filter_view' 等
    config_json JSON NOT NULL, -- 配置内容
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER, -- 创建者用户ID
    updated_by INTEGER, -- 更新者用户ID
    UNIQUE (user_id, module, config_type)
);

COMMENT ON TABLE user_display_preferences IS '用户显示偏好配置表 - 存储用户在各模块中的界面显示配置';
COMMENT ON COLUMN user_display_preferences.user_id IS '用户ID';
COMMENT ON COLUMN user_display_preferences.module IS '模块名称，如：certificate, session, alert';
COMMENT ON COLUMN user_display_preferences.config_type IS '配置类型，如：table_view, chart_view';
COMMENT ON COLUMN user_display_preferences.config_json IS '配置内容JSON，包含列配置、排序、分页等';
COMMENT ON COLUMN user_display_preferences.created_by IS '配置创建者用户ID';
COMMENT ON COLUMN user_display_preferences.updated_by IS '配置更新者用户ID';

-- ========================================
-- 数据导出管理相关表
-- ========================================

-- 数据导出任务表
DROP TABLE IF EXISTS data_export_task CASCADE;

CREATE TABLE data_export_task (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT,
    show_query VARCHAR(2048),
    type INTEGER NOT NULL DEFAULT 0,
    session_id TEXT,
    state INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status INTEGER NOT NULL DEFAULT 1,
    task_id VARCHAR(200)
);

COMMENT ON TABLE data_export_task IS '数据导出任务表';
COMMENT ON COLUMN data_export_task.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task.path IS '文件路径';
COMMENT ON COLUMN data_export_task.query IS 'ES 下载 检索条件';
COMMENT ON COLUMN data_export_task.show_query IS '前端展示条件';
COMMENT ON COLUMN data_export_task.type IS '全量下载为1，部分下载为0';
COMMENT ON COLUMN data_export_task.session_id IS 'session 列表信息';
COMMENT ON COLUMN data_export_task.state IS '0 准备数据 1可下载 2重新下载 3已删除 4待删除';
COMMENT ON COLUMN data_export_task.status IS '数据状态 0 删除 1存在';
COMMENT ON COLUMN data_export_task.task_id IS '任务ID（数组）';

-- 数据导出任务注册表
DROP TABLE IF EXISTS data_export_task_register CASCADE;

CREATE TABLE data_export_task_register (
    id SERIAL PRIMARY KEY,
    created_by INTEGER NOT NULL,
    path VARCHAR(255),
    query TEXT NOT NULL,
    type INTEGER NOT NULL,
    download_count INTEGER NOT NULL DEFAULT 0,
    delete_time TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_type INTEGER NOT NULL DEFAULT 1,
    status INTEGER NOT NULL DEFAULT 1,
    error_msg VARCHAR(255)
);

COMMENT ON TABLE data_export_task_register IS '数据导出任务注册表';
COMMENT ON COLUMN data_export_task_register.created_by IS '创建者用户ID';
COMMENT ON COLUMN data_export_task_register.path IS '日志保存路径';
COMMENT ON COLUMN data_export_task_register.query IS '查询条件';
COMMENT ON COLUMN data_export_task_register.type IS '日志状态，1 待执行，2 准备数据，3 待下载，4 已删除，-1错误';
COMMENT ON COLUMN data_export_task_register.download_count IS '下载次数';
COMMENT ON COLUMN data_export_task_register.task_type IS '1 会话分析、2 会话聚合、3 元数据_SSL、4 元数据_HTTP、5 元数据_DNS';
COMMENT ON COLUMN data_export_task_register.status IS '0 已删除 1 存在';
COMMENT ON COLUMN data_export_task_register.error_msg IS '任务失败的原因';

-- ========================================
-- 系统组件配置相关表
-- ========================================

-- Elasticsearch字段配置表
DROP TABLE IF EXISTS elasticsearch_field_config CASCADE;

CREATE TABLE elasticsearch_field_config (
    id SERIAL PRIMARY KEY,
    es_field TEXT NOT NULL
);

COMMENT ON TABLE elasticsearch_field_config IS 'Elasticsearch字段配置表';

-- 日志插件配置表
DROP TABLE IF EXISTS log_plugin_config CASCADE;

CREATE TABLE log_plugin_config (
    id BIGSERIAL PRIMARY KEY,
    plug_name TEXT NOT NULL,
    plug_type VARCHAR(255) NOT NULL,
    plug_json JSON NOT NULL,
    plug_remark TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    plug_hash VARCHAR(255) NOT NULL
);

COMMENT ON TABLE log_plugin_config IS '日志插件配置表';

-- 网络设备配置表
DROP TABLE IF EXISTS network_device_config CASCADE;

CREATE TABLE network_device_config (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    device_name TEXT NOT NULL,
    mac VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_device_config IS '网络设备配置表';
COMMENT ON COLUMN network_device_config.task_id IS '任务ID';
COMMENT ON COLUMN network_device_config.device_name IS '设备名称';
COMMENT ON COLUMN network_device_config.mac IS 'mac';

-- 网络流量配置表
DROP TABLE IF EXISTS network_flow_config CASCADE;

CREATE TABLE network_flow_config (
    id SERIAL PRIMARY KEY,
    pcie_id VARCHAR(24) NOT NULL,
    flow_name TEXT NOT NULL,
    network_type TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_flow_config IS '网络流量配置表';
COMMENT ON COLUMN network_flow_config.pcie_id IS '任务ID';
COMMENT ON COLUMN network_flow_config.flow_name IS '任务名称';
COMMENT ON COLUMN network_flow_config.network_type IS '任务备注';

-- ========================================
-- 告警和通知管理相关表
-- ========================================

-- 告警输出配置表
DROP TABLE IF EXISTS alarm_output_config CASCADE;

CREATE TABLE alarm_output_config (
    id SERIAL PRIMARY KEY,
    tool VARCHAR(256),
    status INTEGER,
    ip VARCHAR(256),
    port VARCHAR(256)
);

COMMENT ON TABLE alarm_output_config IS '告警输出配置表';
COMMENT ON COLUMN alarm_output_config.tool IS '告警外发组件';
COMMENT ON COLUMN alarm_output_config.status IS '组件启用状态';
COMMENT ON COLUMN alarm_output_config.ip IS '组件IP';
COMMENT ON COLUMN alarm_output_config.port IS '组件端口';

-- 告警订阅规则表
DROP TABLE IF EXISTS alarm_subscription CASCADE;

CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY,
    user_id VARCHAR(32) NOT NULL,
    subscription_name VARCHAR(100) NOT NULL,
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    priority_level INT DEFAULT 1,

    -- 匹配规则 (JSON格式存储)
    match_rules JSONB,

    -- 通知设置
    notification_channels JSONB,
    frequency_type VARCHAR(20) DEFAULT 'REAL_TIME',
    frequency_config JSONB,

    -- 免打扰设置
    quiet_hours_enabled BOOLEAN DEFAULT FALSE,
    quiet_hours_config JSONB,

    -- 统计信息
    trigger_count INT DEFAULT 0,
    last_triggered_time TIMESTAMP,

    -- 审计字段
    created_by VARCHAR(32),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_subscription IS '告警订阅规则表';
COMMENT ON COLUMN alarm_subscription.id IS '订阅ID';
COMMENT ON COLUMN alarm_subscription.user_id IS '用户ID';
COMMENT ON COLUMN alarm_subscription.subscription_name IS '订阅名称';
COMMENT ON COLUMN alarm_subscription.description IS '订阅描述';
COMMENT ON COLUMN alarm_subscription.enabled IS '是否启用';
COMMENT ON COLUMN alarm_subscription.priority_level IS '优先级(1-5)';
COMMENT ON COLUMN alarm_subscription.match_rules IS '匹配规则列表';
COMMENT ON COLUMN alarm_subscription.notification_channels IS '通知渠道配置';
COMMENT ON COLUMN alarm_subscription.frequency_type IS '通知频率类型';
COMMENT ON COLUMN alarm_subscription.frequency_config IS '频率控制配置';
COMMENT ON COLUMN alarm_subscription.quiet_hours_enabled IS '是否启用免打扰';
COMMENT ON COLUMN alarm_subscription.quiet_hours_config IS '免打扰时间配置';
COMMENT ON COLUMN alarm_subscription.trigger_count IS '触发次数';
COMMENT ON COLUMN alarm_subscription.last_triggered_time IS '最后触发时间';
COMMENT ON COLUMN alarm_subscription.created_by IS '创建人';
COMMENT ON COLUMN alarm_subscription.created_time IS '创建时间';
COMMENT ON COLUMN alarm_subscription.updated_by IS '更新人';
COMMENT ON COLUMN alarm_subscription.updated_time IS '更新时间';

-- 通知模板表
DROP TABLE IF EXISTS notification_template CASCADE;

CREATE TABLE notification_template (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(100) NOT NULL,
    template_type VARCHAR(20) NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,

    -- 模板内容
    subject_template TEXT,
    content_template TEXT NOT NULL,
    template_variables JSONB,

    -- 审计字段
    created_by VARCHAR(32),
    created_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(32),
    updated_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE notification_template IS '通知模板表';
COMMENT ON COLUMN notification_template.id IS '模板ID';
COMMENT ON COLUMN notification_template.template_name IS '模板名称';
COMMENT ON COLUMN notification_template.template_type IS '模板类型(EMAIL/KAFKA)';
COMMENT ON COLUMN notification_template.is_default IS '是否为默认模板';
COMMENT ON COLUMN notification_template.subject_template IS '主题模板';
COMMENT ON COLUMN notification_template.content_template IS '内容模板';
COMMENT ON COLUMN notification_template.template_variables IS '模板变量说明';
COMMENT ON COLUMN notification_template.created_by IS '创建人';
COMMENT ON COLUMN notification_template.created_time IS '创建时间';
COMMENT ON COLUMN notification_template.updated_by IS '更新人';
COMMENT ON COLUMN notification_template.updated_time IS '更新时间';

-- 通知发送记录表
DROP TABLE IF EXISTS notification_log CASCADE;

CREATE TABLE notification_log (
    id VARCHAR(32) PRIMARY KEY,
    subscription_id VARCHAR(32) NOT NULL,
    alarm_id VARCHAR(32) NOT NULL,
    channel_type VARCHAR(20) NOT NULL,
    recipient VARCHAR(200) NOT NULL,

    -- 发送状态
    send_status VARCHAR(20) NOT NULL,
    send_time TIMESTAMP NOT NULL,
    error_message TEXT,
    retry_count INT DEFAULT 0,

    -- 内容信息
    subject VARCHAR(500),
    content TEXT
);

COMMENT ON TABLE notification_log IS '通知发送记录表';
COMMENT ON COLUMN notification_log.id IS '记录ID';
COMMENT ON COLUMN notification_log.subscription_id IS '订阅ID';
COMMENT ON COLUMN notification_log.alarm_id IS '告警ID';
COMMENT ON COLUMN notification_log.channel_type IS '通知渠道类型';
COMMENT ON COLUMN notification_log.recipient IS '接收者';
COMMENT ON COLUMN notification_log.send_status IS '发送状态(SUCCESS/FAILED/PENDING)';
COMMENT ON COLUMN notification_log.send_time IS '发送时间';
COMMENT ON COLUMN notification_log.error_message IS '错误信息';
COMMENT ON COLUMN notification_log.retry_count IS '重试次数';
COMMENT ON COLUMN notification_log.subject IS '通知主题';
COMMENT ON COLUMN notification_log.content IS '通知内容';

-- ========================================
-- 管理配置相关扩展表
-- ========================================

-- 设备IP映射表
DROP TABLE IF EXISTS device_ip_mapping CASCADE;

CREATE TABLE device_ip_mapping (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    device_name VARCHAR(255) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    mac_address VARCHAR(17),
    device_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE device_ip_mapping IS '设备IP映射表';
COMMENT ON COLUMN device_ip_mapping.task_id IS '任务ID';
COMMENT ON COLUMN device_ip_mapping.device_name IS '设备名称';
COMMENT ON COLUMN device_ip_mapping.ip_address IS 'IP地址';
COMMENT ON COLUMN device_ip_mapping.mac_address IS 'MAC地址';
COMMENT ON COLUMN device_ip_mapping.device_type IS '设备类型';

-- 分析插件表
DROP TABLE IF EXISTS analysis_plugin CASCADE;

CREATE TABLE analysis_plugin (
    id SERIAL PRIMARY KEY,
    plugin_name VARCHAR(255) NOT NULL,
    plugin_type VARCHAR(50) NOT NULL,
    plugin_version VARCHAR(20),
    plugin_config JSON,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE analysis_plugin IS '分析插件表';
COMMENT ON COLUMN analysis_plugin.plugin_name IS '插件名称';
COMMENT ON COLUMN analysis_plugin.plugin_type IS '插件类型';
COMMENT ON COLUMN analysis_plugin.plugin_version IS '插件版本';
COMMENT ON COLUMN analysis_plugin.plugin_config IS '插件配置（JSON格式）';
COMMENT ON COLUMN analysis_plugin.is_enabled IS '是否启用';

-- 目标组表
DROP TABLE IF EXISTS target_group CASCADE;

CREATE TABLE target_group (
    id SERIAL PRIMARY KEY,
    group_name VARCHAR(255) NOT NULL,
    group_description TEXT,
    target_list TEXT[],
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE target_group IS '目标组表';
COMMENT ON COLUMN target_group.group_name IS '目标组名称';
COMMENT ON COLUMN target_group.group_description IS '目标组描述';
COMMENT ON COLUMN target_group.target_list IS '目标列表（数组）';

-- 目标备注表
DROP TABLE IF EXISTS target_remark CASCADE;

CREATE TABLE target_remark (
    id SERIAL PRIMARY KEY,
    target_id VARCHAR(255) NOT NULL,
    target_type VARCHAR(50) NOT NULL,
    remark TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE target_remark IS '目标备注表';
COMMENT ON COLUMN target_remark.target_id IS '目标ID';
COMMENT ON COLUMN target_remark.target_type IS '目标类型';
COMMENT ON COLUMN target_remark.remark IS '备注信息';
