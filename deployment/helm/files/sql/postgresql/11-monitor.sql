-- ========================================
-- NTA 3.0 系统监控模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 系统监控模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 监控服务相关表
-- ========================================

-- 监控指标定义表
DROP TABLE IF EXISTS monitor_metrics CASCADE;

CREATE TABLE monitor_metrics (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(255) UNIQUE NOT NULL,
    metric_type VARCHAR(50) NOT NULL,
    description TEXT,
    unit VARCHAR(20),
    collection_interval INTEGER DEFAULT 60,
    retention_days INTEGER DEFAULT 30,
    enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE monitor_metrics IS '监控指标定义表';
COMMENT ON COLUMN monitor_metrics.id IS '指标ID';
COMMENT ON COLUMN monitor_metrics.metric_name IS '指标名称（唯一）';
COMMENT ON COLUMN monitor_metrics.metric_type IS '指标类型: COUNTER, GAUGE, HISTOGRAM';
COMMENT ON COLUMN monitor_metrics.description IS '指标描述';
COMMENT ON COLUMN monitor_metrics.unit IS '指标单位';
COMMENT ON COLUMN monitor_metrics.collection_interval IS '采集间隔(秒)';
COMMENT ON COLUMN monitor_metrics.retention_days IS '数据保留天数';
COMMENT ON COLUMN monitor_metrics.enabled IS '是否启用';
COMMENT ON COLUMN monitor_metrics.created_at IS '创建时间';
COMMENT ON COLUMN monitor_metrics.updated_at IS '更新时间';
COMMENT ON COLUMN monitor_metrics.created_by IS '创建者用户ID';
COMMENT ON COLUMN monitor_metrics.updated_by IS '更新者用户ID';

-- 监控数据表
DROP TABLE IF EXISTS monitor_data CASCADE;

CREATE TABLE monitor_data (
    id BIGSERIAL PRIMARY KEY,
    metric_name VARCHAR(255) NOT NULL,
    metric_value DOUBLE PRECISION NOT NULL,
    labels JSONB,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE monitor_data IS '监控数据表';
COMMENT ON COLUMN monitor_data.id IS '数据ID';
COMMENT ON COLUMN monitor_data.metric_name IS '指标名称';
COMMENT ON COLUMN monitor_data.metric_value IS '指标值';
COMMENT ON COLUMN monitor_data.labels IS '标签信息（JSON格式）';
COMMENT ON COLUMN monitor_data.timestamp IS '数据时间戳';
COMMENT ON COLUMN monitor_data.created_at IS '创建时间';

-- 监控告警规则表
DROP TABLE IF EXISTS monitor_alert_rules CASCADE;

CREATE TABLE monitor_alert_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    metric_name VARCHAR(255) NOT NULL,
    condition_operator VARCHAR(10) NOT NULL, -- >, <, >=, <=, =, !=
    threshold_value DOUBLE PRECISION NOT NULL,
    duration_seconds INTEGER DEFAULT 300, -- 持续时间（秒）
    severity VARCHAR(20) DEFAULT 'WARNING', -- CRITICAL, WARNING, INFO
    description TEXT,
    enabled BOOLEAN DEFAULT TRUE,
    notification_channels TEXT[], -- 通知渠道
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE monitor_alert_rules IS '监控告警规则表';
COMMENT ON COLUMN monitor_alert_rules.id IS '规则ID';
COMMENT ON COLUMN monitor_alert_rules.rule_name IS '规则名称';
COMMENT ON COLUMN monitor_alert_rules.metric_name IS '监控指标名称';
COMMENT ON COLUMN monitor_alert_rules.condition_operator IS '条件操作符';
COMMENT ON COLUMN monitor_alert_rules.threshold_value IS '阈值';
COMMENT ON COLUMN monitor_alert_rules.duration_seconds IS '持续时间（秒）';
COMMENT ON COLUMN monitor_alert_rules.severity IS '严重程度';
COMMENT ON COLUMN monitor_alert_rules.description IS '规则描述';
COMMENT ON COLUMN monitor_alert_rules.enabled IS '是否启用';
COMMENT ON COLUMN monitor_alert_rules.notification_channels IS '通知渠道列表';

-- 监控告警记录表
DROP TABLE IF EXISTS monitor_alerts CASCADE;

CREATE TABLE monitor_alerts (
    id BIGSERIAL PRIMARY KEY,
    rule_id INTEGER NOT NULL REFERENCES monitor_alert_rules(id),
    metric_name VARCHAR(255) NOT NULL,
    current_value DOUBLE PRECISION NOT NULL,
    threshold_value DOUBLE PRECISION NOT NULL,
    severity VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'FIRING', -- FIRING, RESOLVED
    started_at TIMESTAMP NOT NULL,
    resolved_at TIMESTAMP,
    description TEXT,
    labels JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE monitor_alerts IS '监控告警记录表';
COMMENT ON COLUMN monitor_alerts.id IS '告警ID';
COMMENT ON COLUMN monitor_alerts.rule_id IS '触发的规则ID';
COMMENT ON COLUMN monitor_alerts.metric_name IS '指标名称';
COMMENT ON COLUMN monitor_alerts.current_value IS '当前值';
COMMENT ON COLUMN monitor_alerts.threshold_value IS '阈值';
COMMENT ON COLUMN monitor_alerts.severity IS '严重程度';
COMMENT ON COLUMN monitor_alerts.status IS '告警状态';
COMMENT ON COLUMN monitor_alerts.started_at IS '告警开始时间';
COMMENT ON COLUMN monitor_alerts.resolved_at IS '告警解决时间';
COMMENT ON COLUMN monitor_alerts.description IS '告警描述';
COMMENT ON COLUMN monitor_alerts.labels IS '标签信息';

-- 系统性能监控表
DROP TABLE IF EXISTS system_performance CASCADE;

CREATE TABLE system_performance (
    id BIGSERIAL PRIMARY KEY,
    hostname VARCHAR(255) NOT NULL,
    cpu_usage DOUBLE PRECISION,
    memory_usage DOUBLE PRECISION,
    disk_usage DOUBLE PRECISION,
    network_in_bytes BIGINT,
    network_out_bytes BIGINT,
    load_average DOUBLE PRECISION,
    process_count INTEGER,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_performance IS '系统性能监控表';
COMMENT ON COLUMN system_performance.id IS '记录ID';
COMMENT ON COLUMN system_performance.hostname IS '主机名';
COMMENT ON COLUMN system_performance.cpu_usage IS 'CPU使用率（百分比）';
COMMENT ON COLUMN system_performance.memory_usage IS '内存使用率（百分比）';
COMMENT ON COLUMN system_performance.disk_usage IS '磁盘使用率（百分比）';
COMMENT ON COLUMN system_performance.network_in_bytes IS '网络入流量（字节）';
COMMENT ON COLUMN system_performance.network_out_bytes IS '网络出流量（字节）';
COMMENT ON COLUMN system_performance.load_average IS '系统负载平均值';
COMMENT ON COLUMN system_performance.process_count IS '进程数量';
COMMENT ON COLUMN system_performance.timestamp IS '监控时间戳';

-- 应用性能监控表
DROP TABLE IF EXISTS application_performance CASCADE;

CREATE TABLE application_performance (
    id BIGSERIAL PRIMARY KEY,
    application_name VARCHAR(255) NOT NULL,
    instance_id VARCHAR(255),
    response_time_ms DOUBLE PRECISION,
    throughput_rps DOUBLE PRECISION,
    error_rate DOUBLE PRECISION,
    active_connections INTEGER,
    memory_usage_mb DOUBLE PRECISION,
    gc_time_ms DOUBLE PRECISION,
    thread_count INTEGER,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE application_performance IS '应用性能监控表';
COMMENT ON COLUMN application_performance.id IS '记录ID';
COMMENT ON COLUMN application_performance.application_name IS '应用名称';
COMMENT ON COLUMN application_performance.instance_id IS '实例ID';
COMMENT ON COLUMN application_performance.response_time_ms IS '响应时间（毫秒）';
COMMENT ON COLUMN application_performance.throughput_rps IS '吞吐量（请求/秒）';
COMMENT ON COLUMN application_performance.error_rate IS '错误率（百分比）';
COMMENT ON COLUMN application_performance.active_connections IS '活跃连接数';
COMMENT ON COLUMN application_performance.memory_usage_mb IS '内存使用量（MB）';
COMMENT ON COLUMN application_performance.gc_time_ms IS 'GC时间（毫秒）';
COMMENT ON COLUMN application_performance.thread_count IS '线程数';
COMMENT ON COLUMN application_performance.timestamp IS '监控时间戳';

-- 数据库性能监控表
DROP TABLE IF EXISTS database_performance CASCADE;

CREATE TABLE database_performance (
    id BIGSERIAL PRIMARY KEY,
    database_name VARCHAR(255) NOT NULL,
    instance_name VARCHAR(255),
    connection_count INTEGER,
    active_queries INTEGER,
    slow_queries INTEGER,
    query_avg_time_ms DOUBLE PRECISION,
    lock_waits INTEGER,
    deadlocks INTEGER,
    cache_hit_ratio DOUBLE PRECISION,
    disk_reads BIGINT,
    disk_writes BIGINT,
    timestamp TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE database_performance IS '数据库性能监控表';
COMMENT ON COLUMN database_performance.id IS '记录ID';
COMMENT ON COLUMN database_performance.database_name IS '数据库名称';
COMMENT ON COLUMN database_performance.instance_name IS '实例名称';
COMMENT ON COLUMN database_performance.connection_count IS '连接数';
COMMENT ON COLUMN database_performance.active_queries IS '活跃查询数';
COMMENT ON COLUMN database_performance.slow_queries IS '慢查询数';
COMMENT ON COLUMN database_performance.query_avg_time_ms IS '平均查询时间（毫秒）';
COMMENT ON COLUMN database_performance.lock_waits IS '锁等待数';
COMMENT ON COLUMN database_performance.deadlocks IS '死锁数';
COMMENT ON COLUMN database_performance.cache_hit_ratio IS '缓存命中率';
COMMENT ON COLUMN database_performance.disk_reads IS '磁盘读取次数';
COMMENT ON COLUMN database_performance.disk_writes IS '磁盘写入次数';
COMMENT ON COLUMN database_performance.timestamp IS '监控时间戳';

-- 监控配置表
DROP TABLE IF EXISTS monitor_config CASCADE;

CREATE TABLE monitor_config (
    id SERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(50) DEFAULT 'STRING', -- STRING, INTEGER, BOOLEAN, JSON
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE monitor_config IS '监控配置表';
COMMENT ON COLUMN monitor_config.id IS '配置ID';
COMMENT ON COLUMN monitor_config.config_key IS '配置键';
COMMENT ON COLUMN monitor_config.config_value IS '配置值';
COMMENT ON COLUMN monitor_config.config_type IS '配置类型';
COMMENT ON COLUMN monitor_config.description IS '配置描述';
COMMENT ON COLUMN monitor_config.is_active IS '是否激活';

-- ========================================
-- 创建索引
-- ========================================

-- 监控指标定义表索引
CREATE INDEX idx_monitor_metrics_metric_name ON monitor_metrics (metric_name);
CREATE INDEX idx_monitor_metrics_metric_type ON monitor_metrics (metric_type);
CREATE INDEX idx_monitor_metrics_enabled ON monitor_metrics (enabled);
CREATE INDEX idx_monitor_metrics_created_at ON monitor_metrics (created_at);

-- 监控数据表索引
CREATE INDEX idx_monitor_data_metric_name ON monitor_data (metric_name);
CREATE INDEX idx_monitor_data_timestamp ON monitor_data (timestamp);
CREATE INDEX idx_monitor_data_created_at ON monitor_data (created_at);
CREATE INDEX idx_monitor_data_metric_timestamp ON monitor_data (metric_name, timestamp);

-- 监控告警规则表索引
CREATE INDEX idx_monitor_alert_rules_metric_name ON monitor_alert_rules (metric_name);
CREATE INDEX idx_monitor_alert_rules_enabled ON monitor_alert_rules (enabled);
CREATE INDEX idx_monitor_alert_rules_severity ON monitor_alert_rules (severity);

-- 监控告警记录表索引
CREATE INDEX idx_monitor_alerts_rule_id ON monitor_alerts (rule_id);
CREATE INDEX idx_monitor_alerts_metric_name ON monitor_alerts (metric_name);
CREATE INDEX idx_monitor_alerts_status ON monitor_alerts (status);
CREATE INDEX idx_monitor_alerts_started_at ON monitor_alerts (started_at);
CREATE INDEX idx_monitor_alerts_severity ON monitor_alerts (severity);

-- 系统性能监控表索引
CREATE INDEX idx_system_performance_hostname ON system_performance (hostname);
CREATE INDEX idx_system_performance_timestamp ON system_performance (timestamp);
CREATE INDEX idx_system_performance_hostname_timestamp ON system_performance (hostname, timestamp);

-- 应用性能监控表索引
CREATE INDEX idx_application_performance_app_name ON application_performance (application_name);
CREATE INDEX idx_application_performance_timestamp ON application_performance (timestamp);
CREATE INDEX idx_application_performance_app_timestamp ON application_performance (application_name, timestamp);

-- 数据库性能监控表索引
CREATE INDEX idx_database_performance_db_name ON database_performance (database_name);
CREATE INDEX idx_database_performance_timestamp ON database_performance (timestamp);
CREATE INDEX idx_database_performance_db_timestamp ON database_performance (database_name, timestamp);

-- 监控配置表索引
CREATE INDEX idx_monitor_config_config_key ON monitor_config (config_key);
CREATE INDEX idx_monitor_config_is_active ON monitor_config (is_active);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为监控相关表创建更新时间触发器
CREATE TRIGGER update_monitor_metrics_updated_at 
    BEFORE UPDATE ON monitor_metrics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_alert_rules_updated_at 
    BEFORE UPDATE ON monitor_alert_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_alerts_updated_at 
    BEFORE UPDATE ON monitor_alerts 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_monitor_config_updated_at 
    BEFORE UPDATE ON monitor_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 插入默认监控配置数据
-- ========================================

-- 插入默认监控配置
INSERT INTO monitor_config (config_key, config_value, config_type, description) VALUES
('data_retention_days', '30', 'INTEGER', '监控数据保留天数'),
('alert_check_interval', '60', 'INTEGER', '告警检查间隔（秒）'),
('performance_collection_interval', '300', 'INTEGER', '性能数据采集间隔（秒）'),
('max_alert_history', '10000', 'INTEGER', '最大告警历史记录数'),
('enable_email_notifications', 'true', 'BOOLEAN', '是否启用邮件通知'),
('enable_webhook_notifications', 'true', 'BOOLEAN', '是否启用Webhook通知'),
('system_monitoring_enabled', 'true', 'BOOLEAN', '是否启用系统监控'),
('application_monitoring_enabled', 'true', 'BOOLEAN', '是否启用应用监控'),
('database_monitoring_enabled', 'true', 'BOOLEAN', '是否启用数据库监控');

-- 插入默认监控指标
INSERT INTO monitor_metrics (metric_name, metric_type, description, unit, collection_interval) VALUES
('system_cpu_usage', 'GAUGE', '系统CPU使用率', 'percent', 60),
('system_memory_usage', 'GAUGE', '系统内存使用率', 'percent', 60),
('system_disk_usage', 'GAUGE', '系统磁盘使用率', 'percent', 300),
('system_load_average', 'GAUGE', '系统负载平均值', 'count', 60),
('application_response_time', 'HISTOGRAM', '应用响应时间', 'milliseconds', 30),
('application_throughput', 'GAUGE', '应用吞吐量', 'requests_per_second', 30),
('application_error_rate', 'GAUGE', '应用错误率', 'percent', 30),
('database_connection_count', 'GAUGE', '数据库连接数', 'count', 60),
('database_query_time', 'HISTOGRAM', '数据库查询时间', 'milliseconds', 60),
('network_traffic_in', 'COUNTER', '网络入流量', 'bytes', 60),
('network_traffic_out', 'COUNTER', '网络出流量', 'bytes', 60);

-- 插入默认告警规则
INSERT INTO monitor_alert_rules (rule_name, metric_name, condition_operator, threshold_value, duration_seconds, severity, description) VALUES
('高CPU使用率告警', 'system_cpu_usage', '>', 80.0, 300, 'WARNING', 'CPU使用率超过80%'),
('极高CPU使用率告警', 'system_cpu_usage', '>', 95.0, 60, 'CRITICAL', 'CPU使用率超过95%'),
('高内存使用率告警', 'system_memory_usage', '>', 85.0, 300, 'WARNING', '内存使用率超过85%'),
('极高内存使用率告警', 'system_memory_usage', '>', 95.0, 60, 'CRITICAL', '内存使用率超过95%'),
('高磁盘使用率告警', 'system_disk_usage', '>', 90.0, 600, 'WARNING', '磁盘使用率超过90%'),
('应用响应时间过长告警', 'application_response_time', '>', 5000.0, 180, 'WARNING', '应用响应时间超过5秒'),
('应用错误率过高告警', 'application_error_rate', '>', 5.0, 300, 'WARNING', '应用错误率超过5%'),
('数据库连接数过多告警', 'database_connection_count', '>', 100, 300, 'WARNING', '数据库连接数超过100');

-- ========================================
-- 监控相关扩展表
-- ========================================

-- 数据包缓存统计表
DROP TABLE IF EXISTS packet_buffer_statistics CASCADE;

CREATE TABLE packet_buffer_statistics (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    buffer_size BIGINT NOT NULL,
    used_size BIGINT NOT NULL,
    packet_count INTEGER NOT NULL,
    drop_count INTEGER NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE packet_buffer_statistics IS '数据包缓存统计表';
COMMENT ON COLUMN packet_buffer_statistics.task_id IS '任务ID';
COMMENT ON COLUMN packet_buffer_statistics.buffer_size IS '缓存总大小（字节）';
COMMENT ON COLUMN packet_buffer_statistics.used_size IS '已使用大小（字节）';
COMMENT ON COLUMN packet_buffer_statistics.packet_count IS '缓存包数量';
COMMENT ON COLUMN packet_buffer_statistics.drop_count IS '丢包数量';

-- 为新增表创建索引
CREATE INDEX idx_packet_buffer_statistics_task_id ON packet_buffer_statistics (task_id);
CREATE INDEX idx_packet_buffer_statistics_created_at ON packet_buffer_statistics (created_at);