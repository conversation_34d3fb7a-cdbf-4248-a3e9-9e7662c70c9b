-- ========================================
-- NTA 3.0 规则管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 规则管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 过滤规则管理表
-- ========================================

-- 过滤规则表
DROP TABLE IF EXISTS filter_rule CASCADE;

CREATE TABLE filter_rule (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    filter_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    criteria filter_criteria_enum NOT NULL,
    active BOOLEAN NOT NULL DEFAULT true
);

COMMENT ON TABLE filter_rule IS '过滤规则表';
COMMENT ON COLUMN filter_rule.task_id IS '任务ID';
COMMENT ON COLUMN filter_rule.ip IS 'IP地址';
COMMENT ON COLUMN filter_rule.filter_json IS '过滤规则JSON字符串';
COMMENT ON COLUMN filter_rule.created_at IS '创建时间';
COMMENT ON COLUMN filter_rule.updated_at IS '更新时间';
COMMENT ON COLUMN filter_rule.hash IS '规则hash值，用于校验重复';
COMMENT ON COLUMN filter_rule.criteria IS '过滤条件，使用filter_criteria_enum枚举';
COMMENT ON COLUMN filter_rule.active IS '是否激活 true:正常 false:删除';

-- ========================================
-- DDoS防护相关表
-- ========================================

-- DDoS防护配置表
DROP TABLE IF EXISTS ddos_protection_config CASCADE;

CREATE TABLE ddos_protection_config (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    doss_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    state INTEGER NOT NULL DEFAULT 1
);

COMMENT ON TABLE ddos_protection_config IS 'DDoS防护配置表';
COMMENT ON COLUMN ddos_protection_config.task_id IS '任务ID';
COMMENT ON COLUMN ddos_protection_config.ip IS 'IP地址';
COMMENT ON COLUMN ddos_protection_config.doss_json IS 'DDoS配置JSON';
COMMENT ON COLUMN ddos_protection_config.hash IS '配置hash值';
COMMENT ON COLUMN ddos_protection_config.state IS '状态（1=启用，0=禁用）';

-- DDoS防护状态表
DROP TABLE IF EXISTS ddos_protection_state CASCADE;

CREATE TABLE ddos_protection_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    ddos_param_json JSON NOT NULL
);

COMMENT ON TABLE ddos_protection_state IS 'DDoS防护状态表';
COMMENT ON COLUMN ddos_protection_state.task_id IS '任务ID';
COMMENT ON COLUMN ddos_protection_state.state IS '0表示停用，1表示开启';

-- DDoS防护统计表
DROP TABLE IF EXISTS ddos_protection_statistics CASCADE;

CREATE TABLE ddos_protection_statistics (
    id SERIAL PRIMARY KEY,
    ddos_type INTEGER NOT NULL,
    task_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE ddos_protection_statistics IS 'DDoS防护统计信息，按时间统计';
COMMENT ON COLUMN ddos_protection_statistics.ddos_type IS 'DDoS类型';
COMMENT ON COLUMN ddos_protection_statistics.task_id IS '任务ID';
COMMENT ON COLUMN ddos_protection_statistics.packet_num IS '包数量';

-- 过滤模式表
DROP TABLE IF EXISTS filter_mode CASCADE;

CREATE TABLE filter_mode (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    drop_mode BOOLEAN NOT NULL
);

COMMENT ON TABLE filter_mode IS '过滤模式表';
COMMENT ON COLUMN filter_mode.task_id IS '任务ID';
COMMENT ON COLUMN filter_mode.drop_mode IS '是否丢弃模式 false表示保留，true表示丢弃';

-- 流量过滤统计表
DROP TABLE IF EXISTS traffic_filter_statistics CASCADE;

CREATE TABLE traffic_filter_statistics (
    id SERIAL PRIMARY KEY,
    filter_id INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    bytes BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_filter_statistics IS '流量过滤统计表';
COMMENT ON COLUMN traffic_filter_statistics.filter_id IS '过滤ID';
COMMENT ON COLUMN traffic_filter_statistics.batch_id IS '批次ID';
COMMENT ON COLUMN traffic_filter_statistics.packet_num IS '过滤的包数';
COMMENT ON COLUMN traffic_filter_statistics.bytes IS '过滤掉的字节数';

-- 网络防护状态表
DROP TABLE IF EXISTS network_protection_state CASCADE;

CREATE TABLE network_protection_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    network_param_json JSON NOT NULL
);

COMMENT ON TABLE network_protection_state IS '网络防护状态表';
COMMENT ON COLUMN network_protection_state.task_id IS '任务ID';
COMMENT ON COLUMN network_protection_state.state IS '0表示停用，1表示开启';

-- 网络防护统计表
DROP TABLE IF EXISTS network_protection_statistics CASCADE;

CREATE TABLE network_protection_statistics (
    id SERIAL PRIMARY KEY,
    type INTEGER NOT NULL,
    batch_id INTEGER NOT NULL,
    packet_num INTEGER NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_protection_statistics IS '网络防护统计表';
COMMENT ON COLUMN network_protection_statistics.batch_id IS '批次ID';
COMMENT ON COLUMN network_protection_statistics.packet_num IS '过滤的包数';

-- ========================================
-- 白名单相关表
-- ========================================

-- 内部证书白名单表
DROP TABLE IF EXISTS internal_certificate_whitelist CASCADE;

CREATE TABLE internal_certificate_whitelist (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    certificate_hash VARCHAR(255) NOT NULL,
    certificate_subject VARCHAR(500),
    certificate_issuer VARCHAR(500),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internal_certificate_whitelist IS '内部证书白名单表';
COMMENT ON COLUMN internal_certificate_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_certificate_whitelist.certificate_hash IS '证书哈希值';
COMMENT ON COLUMN internal_certificate_whitelist.certificate_subject IS '证书主题';
COMMENT ON COLUMN internal_certificate_whitelist.certificate_issuer IS '证书颁发者';

-- 内部域名白名单表
DROP TABLE IF EXISTS internal_domain_whitelist CASCADE;

CREATE TABLE internal_domain_whitelist (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    domain_name VARCHAR(255) NOT NULL,
    domain_type VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internal_domain_whitelist IS '内部域名白名单表';
COMMENT ON COLUMN internal_domain_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_domain_whitelist.domain_name IS '域名';
COMMENT ON COLUMN internal_domain_whitelist.domain_type IS '域名类型';

-- 内部IP白名单表
DROP TABLE IF EXISTS internal_ip_whitelist CASCADE;

CREATE TABLE internal_ip_whitelist (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    ip_range VARCHAR(50),
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internal_ip_whitelist IS '内部IP白名单表';
COMMENT ON COLUMN internal_ip_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN internal_ip_whitelist.ip_address IS 'IP地址';
COMMENT ON COLUMN internal_ip_whitelist.ip_range IS 'IP范围';
COMMENT ON COLUMN internal_ip_whitelist.description IS '描述';

-- 流量白名单表
DROP TABLE IF EXISTS traffic_whitelist CASCADE;

CREATE TABLE traffic_whitelist (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    whitelist_name VARCHAR(255) NOT NULL,
    whitelist_type VARCHAR(50) NOT NULL,
    rule_config JSON NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_whitelist IS '流量白名单表';
COMMENT ON COLUMN traffic_whitelist.task_id IS '任务ID';
COMMENT ON COLUMN traffic_whitelist.whitelist_name IS '白名单名称';
COMMENT ON COLUMN traffic_whitelist.whitelist_type IS '白名单类型';
COMMENT ON COLUMN traffic_whitelist.rule_config IS '规则配置（JSON格式）';
COMMENT ON COLUMN traffic_whitelist.is_enabled IS '是否启用';

-- 流量白名单日志表
DROP TABLE IF EXISTS traffic_whitelist_log CASCADE;

CREATE TABLE traffic_whitelist_log (
    id SERIAL PRIMARY KEY,
    whitelist_id INTEGER NOT NULL,
    action VARCHAR(50) NOT NULL,
    matched_traffic JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_whitelist_log IS '流量白名单日志表';
COMMENT ON COLUMN traffic_whitelist_log.whitelist_id IS '白名单ID';
COMMENT ON COLUMN traffic_whitelist_log.action IS '动作（ALLOW, BLOCK等）';
COMMENT ON COLUMN traffic_whitelist_log.matched_traffic IS '匹配的流量信息（JSON格式）';

-- 流量白名单状态表
DROP TABLE IF EXISTS traffic_whitelist_state CASCADE;

CREATE TABLE traffic_whitelist_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    whitelist_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    state_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE traffic_whitelist_state IS '流量白名单状态表';
COMMENT ON COLUMN traffic_whitelist_state.task_id IS '任务ID';
COMMENT ON COLUMN traffic_whitelist_state.whitelist_id IS '白名单ID';
COMMENT ON COLUMN traffic_whitelist_state.state IS '状态（0=停用，1=启用）';
COMMENT ON COLUMN traffic_whitelist_state.state_config IS '状态配置（JSON格式）';

-- 为新增表创建索引
CREATE INDEX idx_internal_certificate_whitelist_task_id ON internal_certificate_whitelist (task_id);
CREATE INDEX idx_internal_certificate_whitelist_hash ON internal_certificate_whitelist (certificate_hash);
CREATE INDEX idx_internal_domain_whitelist_task_id ON internal_domain_whitelist (task_id);
CREATE INDEX idx_internal_domain_whitelist_domain ON internal_domain_whitelist (domain_name);
CREATE INDEX idx_internal_ip_whitelist_task_id ON internal_ip_whitelist (task_id);
CREATE INDEX idx_internal_ip_whitelist_ip ON internal_ip_whitelist (ip_address);
CREATE INDEX idx_traffic_whitelist_task_id ON traffic_whitelist (task_id);
CREATE INDEX idx_traffic_whitelist_type ON traffic_whitelist (whitelist_type);
CREATE INDEX idx_traffic_whitelist_enabled ON traffic_whitelist (is_enabled);
CREATE INDEX idx_traffic_whitelist_log_whitelist_id ON traffic_whitelist_log (whitelist_id);
CREATE INDEX idx_traffic_whitelist_log_created_at ON traffic_whitelist_log (created_at);
CREATE INDEX idx_traffic_whitelist_state_task_id ON traffic_whitelist_state (task_id);
CREATE INDEX idx_traffic_whitelist_state_whitelist_id ON traffic_whitelist_state (whitelist_id);

-- ========================================
-- 检测规则相关表
-- ========================================

-- 检测规则配置表
DROP TABLE IF EXISTS detection_rule_configs CASCADE;

CREATE TABLE detection_rule_configs (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_config JSON NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_configs IS '检测规则配置表';
COMMENT ON COLUMN detection_rule_configs.rule_name IS '规则名称';
COMMENT ON COLUMN detection_rule_configs.rule_type IS '规则类型';
COMMENT ON COLUMN detection_rule_configs.rule_config IS '规则配置（JSON格式）';
COMMENT ON COLUMN detection_rule_configs.is_enabled IS '是否启用';
COMMENT ON COLUMN detection_rule_configs.priority IS '优先级';

-- 检测规则统计表
DROP TABLE IF EXISTS detection_rule_statistics CASCADE;

CREATE TABLE detection_rule_statistics (
    id SERIAL PRIMARY KEY,
    rule_id INTEGER NOT NULL,
    detection_count INTEGER NOT NULL DEFAULT 0,
    hit_count INTEGER NOT NULL DEFAULT 0,
    false_positive_count INTEGER NOT NULL DEFAULT 0,
    last_detection_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_statistics IS '检测规则统计表';
COMMENT ON COLUMN detection_rule_statistics.rule_id IS '规则ID';
COMMENT ON COLUMN detection_rule_statistics.detection_count IS '检测次数';
COMMENT ON COLUMN detection_rule_statistics.hit_count IS '命中次数';
COMMENT ON COLUMN detection_rule_statistics.false_positive_count IS '误报次数';
COMMENT ON COLUMN detection_rule_statistics.last_detection_time IS '最后检测时间';

-- 检测规则库配置表
DROP TABLE IF EXISTS detection_rule_library_config CASCADE;

CREATE TABLE detection_rule_library_config (
    id SERIAL PRIMARY KEY,
    library_name VARCHAR(255) NOT NULL,
    library_version VARCHAR(50),
    library_config JSON NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_library_config IS '检测规则库配置表';
COMMENT ON COLUMN detection_rule_library_config.library_name IS '规则库名称';
COMMENT ON COLUMN detection_rule_library_config.library_version IS '规则库版本';
COMMENT ON COLUMN detection_rule_library_config.library_config IS '规则库配置（JSON格式）';
COMMENT ON COLUMN detection_rule_library_config.is_enabled IS '是否启用';

-- 威胁检测算法配置表
DROP TABLE IF EXISTS threat_detection_algorithm_configs CASCADE;

CREATE TABLE threat_detection_algorithm_configs (
    id SERIAL PRIMARY KEY,
    algorithm_name VARCHAR(255) NOT NULL,
    algorithm_type VARCHAR(50) NOT NULL,
    algorithm_config JSON NOT NULL,
    is_enabled BOOLEAN NOT NULL DEFAULT true,
    threshold_config JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_configs IS '威胁检测算法配置表';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_name IS '算法名称';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_type IS '算法类型';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_config IS '算法配置（JSON格式）';
COMMENT ON COLUMN threat_detection_algorithm_configs.is_enabled IS '是否启用';
COMMENT ON COLUMN threat_detection_algorithm_configs.threshold_config IS '阈值配置（JSON格式）';

-- 威胁检测算法统计表
DROP TABLE IF EXISTS threat_detection_algorithm_statistics CASCADE;

CREATE TABLE threat_detection_algorithm_statistics (
    id SERIAL PRIMARY KEY,
    algorithm_id INTEGER NOT NULL,
    detection_count INTEGER NOT NULL DEFAULT 0,
    threat_count INTEGER NOT NULL DEFAULT 0,
    accuracy_rate DECIMAL(5,2),
    false_positive_rate DECIMAL(5,2),
    last_detection_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_statistics IS '威胁检测算法统计表';
COMMENT ON COLUMN threat_detection_algorithm_statistics.algorithm_id IS '算法ID';
COMMENT ON COLUMN threat_detection_algorithm_statistics.detection_count IS '检测次数';
COMMENT ON COLUMN threat_detection_algorithm_statistics.threat_count IS '威胁检测次数';
COMMENT ON COLUMN threat_detection_algorithm_statistics.accuracy_rate IS '准确率（百分比）';
COMMENT ON COLUMN threat_detection_algorithm_statistics.false_positive_rate IS '误报率（百分比）';
COMMENT ON COLUMN threat_detection_algorithm_statistics.last_detection_time IS '最后检测时间';

-- 为新增表创建索引
CREATE INDEX idx_detection_rule_configs_name ON detection_rule_configs (rule_name);
CREATE INDEX idx_detection_rule_configs_type ON detection_rule_configs (rule_type);
CREATE INDEX idx_detection_rule_configs_enabled ON detection_rule_configs (is_enabled);
CREATE INDEX idx_detection_rule_statistics_rule_id ON detection_rule_statistics (rule_id);
CREATE INDEX idx_detection_rule_statistics_last_detection ON detection_rule_statistics (last_detection_time);
CREATE INDEX idx_detection_rule_library_config_name ON detection_rule_library_config (library_name);
CREATE INDEX idx_detection_rule_library_config_enabled ON detection_rule_library_config (is_enabled);
CREATE INDEX idx_threat_detection_algorithm_configs_name ON threat_detection_algorithm_configs (algorithm_name);
CREATE INDEX idx_threat_detection_algorithm_configs_type ON threat_detection_algorithm_configs (algorithm_type);
CREATE INDEX idx_threat_detection_algorithm_configs_enabled ON threat_detection_algorithm_configs (is_enabled);
CREATE INDEX idx_threat_detection_algorithm_statistics_algorithm_id ON threat_detection_algorithm_statistics (algorithm_id);
CREATE INDEX idx_threat_detection_algorithm_statistics_last_detection ON threat_detection_algorithm_statistics (last_detection_time);

-- ========================================
-- 威胁检测算法配置表
-- ========================================

-- 威胁检测算法配置表
DROP TABLE IF EXISTS threat_detection_algorithm_configs CASCADE;

CREATE TABLE threat_detection_algorithm_configs (
    algorithm_id INTEGER PRIMARY KEY,
    algorithm_name VARCHAR(1024) NOT NULL,
    algorithm_type VARCHAR(512) NOT NULL,
    description VARCHAR(2048) NOT NULL,
    algorithm_hash VARCHAR(255),
    algorithm_version VARCHAR(255),
    algorithm_path VARCHAR(255),
    state INTEGER NOT NULL DEFAULT 1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_configs IS '威胁检测算法配置表';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_id IS '算法ID';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_name IS '算法名称';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_type IS '算法类型（协议识别、特征识别、行为识别、LSTM神经网络、随机森林等）';
COMMENT ON COLUMN threat_detection_algorithm_configs.description IS '算法描述';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_hash IS '算法文件哈希';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_path IS '算法文件路径';
COMMENT ON COLUMN threat_detection_algorithm_configs.state IS '算法状态（1=启用，0=禁用）';
COMMENT ON COLUMN threat_detection_algorithm_configs.retain_metadata IS '是否留存流量元数据';
COMMENT ON COLUMN threat_detection_algorithm_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';

-- 威胁检测算法统计表
DROP TABLE IF EXISTS threat_detection_algorithm_statistics CASCADE;

CREATE TABLE threat_detection_algorithm_statistics (
    algorithm_id INTEGER PRIMARY KEY REFERENCES threat_detection_algorithm_configs (algorithm_id),
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_statistics IS '威胁检测算法统计表';
COMMENT ON COLUMN threat_detection_algorithm_statistics.algorithm_id IS '算法ID';
COMMENT ON COLUMN threat_detection_algorithm_statistics.total_sum_bytes IS '算法命中数据总量';
COMMENT ON COLUMN threat_detection_algorithm_statistics.last_hit_time IS '最新命中时间';

-- ========================================
-- 检测规则配置表
-- ========================================

-- 检测规则配置表
DROP TABLE IF EXISTS detection_rule_configs CASCADE;

CREATE TABLE detection_rule_configs (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    label_id INTEGER NOT NULL UNIQUE,
    threat_level threat_level_enum NOT NULL,
    rule_name TEXT NOT NULL,
    rule_desc TEXT NOT NULL,
    rule_enabled BOOLEAN NOT NULL DEFAULT true,
    rule_source rule_source_enum NOT NULL DEFAULT 'SYSTEM',
    capture_mode capture_mode_enum NOT NULL,
    rule_json JSON NOT NULL,
    rule_hash VARCHAR(255) NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum DEFAULT 'OTHER',
    traffic_rate_limit_bps BIGINT NOT NULL DEFAULT 0,
    traffic_retention_limit_bytes BIGINT NOT NULL DEFAULT -1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    lib_respond_enabled BOOLEAN NOT NULL DEFAULT false,
    lib_respond_lib VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_config VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_session_end BIGINT NOT NULL DEFAULT 0,
    lib_respond_pkt_num BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_configs IS '检测规则配置表';
COMMENT ON COLUMN detection_rule_configs.label_id IS '标签ID（规则命中时打上的标签）';
COMMENT ON COLUMN detection_rule_configs.threat_level IS '威胁等级，使用threat_level_enum枚举';
COMMENT ON COLUMN detection_rule_configs.rule_name IS '规则名称（告警类型）';
COMMENT ON COLUMN detection_rule_configs.rule_desc IS '规则描述';
COMMENT ON COLUMN detection_rule_configs.rule_enabled IS '是否启用规则 true:生效 false:失效';
COMMENT ON COLUMN detection_rule_configs.rule_source IS '规则来源，使用rule_source_enum枚举';
COMMENT ON COLUMN detection_rule_configs.capture_mode IS '采集模式，使用capture_mode_enum枚举';
COMMENT ON COLUMN detection_rule_configs.rule_json IS '规则JSON配置';
COMMENT ON COLUMN detection_rule_configs.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';
COMMENT ON COLUMN detection_rule_configs.traffic_rate_limit_bps IS '流量留存限速，单位：字节/秒';
COMMENT ON COLUMN detection_rule_configs.traffic_retention_limit_bytes IS '流量留存上限，单位：字节（负数表示不限）';
COMMENT ON COLUMN detection_rule_configs.retain_metadata IS '是否留存流量元数据';
COMMENT ON COLUMN detection_rule_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';
COMMENT ON COLUMN detection_rule_configs.lib_respond_enabled IS '是否开启动态库响应';

-- 检测规则统计表
DROP TABLE IF EXISTS detection_rule_statistics CASCADE;

CREATE TABLE detection_rule_statistics (
    rule_id INTEGER PRIMARY KEY REFERENCES detection_rule_configs (rule_id),
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_statistics IS '检测规则统计表';
COMMENT ON COLUMN detection_rule_statistics.rule_id IS '规则ID';
COMMENT ON COLUMN detection_rule_statistics.rule_size IS '规则数据量';
COMMENT ON COLUMN detection_rule_statistics.total_sum_bytes IS '命中数据总量';
COMMENT ON COLUMN detection_rule_statistics.last_hit_time IS '最新命中时间';

-- 检测规则库配置表
DROP TABLE IF EXISTS detection_rule_library_config CASCADE;

CREATE TABLE detection_rule_library_config (
    id BIGSERIAL PRIMARY KEY,
    lib_path TEXT NOT NULL,
    config_path TEXT NOT NULL
);

COMMENT ON TABLE detection_rule_library_config IS '检测规则库配置表';

-- ========================================
-- 规则服务相关表
-- ========================================

-- 规则定义表
DROP TABLE IF EXISTS rule_definitions CASCADE;

CREATE TABLE rule_definitions (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_content TEXT NOT NULL,
    rule_format VARCHAR(20) NOT NULL DEFAULT 'JSON',
    priority INTEGER DEFAULT 0,
    enabled BOOLEAN NOT NULL DEFAULT true,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

COMMENT ON TABLE rule_definitions IS '规则定义表';
COMMENT ON COLUMN rule_definitions.rule_format IS '规则格式: JSON, YAML, DROOLS';
COMMENT ON COLUMN rule_definitions.priority IS '优先级，数值越大优先级越高';

-- 规则执行记录表
DROP TABLE IF EXISTS rule_executions CASCADE;

CREATE TABLE rule_executions (
    id BIGSERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES rule_definitions (id),
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE rule_executions IS '规则执行记录表';
COMMENT ON COLUMN rule_executions.rule_id IS '关联的规则ID';
COMMENT ON COLUMN rule_executions.input_data IS '输入数据（JSON格式）';
COMMENT ON COLUMN rule_executions.output_data IS '输出数据（JSON格式）';
COMMENT ON COLUMN rule_executions.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN rule_executions.status IS '执行状态: SUCCESS, FAILED, TIMEOUT';
COMMENT ON COLUMN rule_executions.error_message IS '错误信息';
COMMENT ON COLUMN rule_executions.executed_at IS '执行时间';

-- ========================================
-- 流量白名单管理表
-- ========================================

-- 流量白名单表
DROP TABLE IF EXISTS traffic_whitelist CASCADE;

CREATE TABLE traffic_whitelist (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(50) NOT NULL DEFAULT '',
    port INTEGER NOT NULL DEFAULT -1,
    app_id INTEGER NOT NULL DEFAULT -1,
    rule_id INTEGER NOT NULL DEFAULT -1,
    rule_level INTEGER NOT NULL DEFAULT -1,
    rule_name TEXT NOT NULL DEFAULT '',
    rule_desc TEXT NOT NULL DEFAULT '',
    rule_state VARCHAR(255) NOT NULL DEFAULT '生效',
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_size_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    capture_mode INTEGER NOT NULL DEFAULT 0,
    rule_json JSON NOT NULL DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rule_hash VARCHAR(255) NOT NULL DEFAULT '',
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'OTHER',
    task_id INTEGER NOT NULL DEFAULT 0
);

COMMENT ON TABLE traffic_whitelist IS '流量白名单表';
COMMENT ON COLUMN traffic_whitelist.server_ip IS '服务器IP';
COMMENT ON COLUMN traffic_whitelist.port IS '端口';
COMMENT ON COLUMN traffic_whitelist.app_id IS '应用id';
COMMENT ON COLUMN traffic_whitelist.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

-- 流量白名单日志表
DROP TABLE IF EXISTS traffic_whitelist_log CASCADE;

CREATE TABLE traffic_whitelist_log (
    id SERIAL PRIMARY KEY,
    last_filter VARCHAR(50) NOT NULL,
    last_total VARCHAR(50) NOT NULL,
    white_list_id INTEGER NOT NULL
);

COMMENT ON TABLE traffic_whitelist_log IS '流量白名单日志表';
COMMENT ON COLUMN traffic_whitelist_log.last_filter IS '上次过滤日志数量';
COMMENT ON COLUMN traffic_whitelist_log.last_total IS '上次全部日志数量';
COMMENT ON COLUMN traffic_whitelist_log.white_list_id IS '外键 white_list 的主键';

-- 流量白名单状态表
DROP TABLE IF EXISTS traffic_whitelist_state CASCADE;

CREATE TABLE traffic_whitelist_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    UNIQUE (task_id)
);

COMMENT ON TABLE traffic_whitelist_state IS '流量白名单状态表';
COMMENT ON COLUMN traffic_whitelist_state.task_id IS '任务ID';
COMMENT ON COLUMN traffic_whitelist_state.state IS '白名单状态：0-停用，1-启用';

-- ========================================
-- 创建索引
-- ========================================

-- 过滤规则表索引
CREATE INDEX idx_filter_rule_task_id ON filter_rule (task_id);
CREATE INDEX idx_filter_rule_ip ON filter_rule (ip);
CREATE INDEX idx_filter_rule_criteria ON filter_rule (criteria);
CREATE INDEX idx_filter_rule_active ON filter_rule (active);
CREATE INDEX idx_filter_rule_hash ON filter_rule (hash);

-- 威胁检测算法配置表索引
CREATE INDEX idx_threat_detection_algorithm_configs_state ON threat_detection_algorithm_configs (state);
CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_type ON threat_detection_algorithm_configs (algorithm_type);
CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_name ON threat_detection_algorithm_configs (algorithm_name);

-- 威胁检测算法统计表索引
CREATE INDEX idx_threat_detection_algorithm_statistics_last_size_time ON threat_detection_algorithm_statistics (last_hit_time);
CREATE INDEX idx_threat_detection_algorithm_statistics_total_sum_bytes ON threat_detection_algorithm_statistics (total_sum_bytes);

-- 检测规则配置表索引
CREATE INDEX idx_detection_rule_configs_task_id ON detection_rule_configs (task_id);
CREATE INDEX idx_detection_rule_configs_rule_state ON detection_rule_configs (rule_enabled);
CREATE INDEX idx_detection_rule_configs_attack_stage ON detection_rule_configs (cyber_kill_chain);
CREATE INDEX idx_detection_rule_configs_rule_level ON detection_rule_configs (threat_level);
CREATE INDEX idx_detection_rule_configs_label_id ON detection_rule_configs (label_id);
CREATE INDEX idx_detection_rule_configs_rule_source ON detection_rule_configs (rule_source);

-- 检测规则统计表索引
CREATE INDEX idx_detection_rule_statistics_last_hit_time ON detection_rule_statistics (last_hit_time);
CREATE INDEX idx_detection_rule_statistics_total_sum_bytes ON detection_rule_statistics (total_sum_bytes);

-- 规则定义表索引
CREATE INDEX idx_rule_definitions_rule_type ON rule_definitions (rule_type);
CREATE INDEX idx_rule_definitions_enabled ON rule_definitions (enabled);
CREATE INDEX idx_rule_definitions_priority ON rule_definitions (priority);
CREATE INDEX idx_rule_definitions_created_by ON rule_definitions (created_by);

-- 规则执行记录表索引
CREATE INDEX idx_rule_executions_rule_id ON rule_executions (rule_id);
CREATE INDEX idx_rule_executions_status ON rule_executions (status);
CREATE INDEX idx_rule_executions_executed_at ON rule_executions (executed_at);

-- 流量白名单表索引
CREATE INDEX idx_traffic_whitelist_server_ip ON traffic_whitelist (server_ip);
CREATE INDEX idx_traffic_whitelist_task_id ON traffic_whitelist (task_id);
CREATE INDEX idx_traffic_whitelist_rule_state ON traffic_whitelist (rule_state);
CREATE INDEX idx_traffic_whitelist_cyber_kill_chain ON traffic_whitelist (cyber_kill_chain);
CREATE INDEX idx_traffic_whitelist_created_at ON traffic_whitelist (created_at);

-- 流量白名单日志表索引
CREATE INDEX idx_traffic_whitelist_log_white_list_id ON traffic_whitelist_log (white_list_id);

-- 流量白名单状态表索引
CREATE INDEX idx_traffic_whitelist_state_task_id ON traffic_whitelist_state (task_id);
CREATE INDEX idx_traffic_whitelist_state_state ON traffic_whitelist_state (state);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为规则管理表创建更新时间触发器
CREATE TRIGGER update_filter_rule_updated_at 
    BEFORE UPDATE ON filter_rule 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_algorithm_configs_updated_at 
    BEFORE UPDATE ON threat_detection_algorithm_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_algorithm_statistics_updated_at 
    BEFORE UPDATE ON threat_detection_algorithm_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_detection_rule_configs_updated_at 
    BEFORE UPDATE ON detection_rule_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_detection_rule_statistics_updated_at 
    BEFORE UPDATE ON detection_rule_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rule_definitions_updated_at
    BEFORE UPDATE ON rule_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为流量白名单表创建更新时间触发器
CREATE TRIGGER update_traffic_whitelist_updated_at
    BEFORE UPDATE ON traffic_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
