-- ========================================
-- NTA 3.0 元数据管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 元数据管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 枚举类型定义（系统元数据）
-- ========================================

-- 威胁类型枚举
CREATE TYPE threat_type_enum AS ENUM (
    'MALWARE',
    'APT',
    'BOTNET',
    'PHISHING',
    'C2',
    'MINING',
    'RANSOMWARE',
    'TROJAN',
    'BACKDOOR',
    'EXPLOIT',
    'OTHER'
);

-- 威胁等级枚举 (0-4级)
CREATE TYPE threat_level_enum AS ENUM ('NONE', 'LOW', 'MEDIUM', 'HIGH', 'CRITICAL');

-- 检测器类型枚举
CREATE TYPE detector_type_enum AS ENUM (
    'CERTIFICATE',
    'NETWORK',
    'DNS',
    'HTTP',
    'SSL',
    'FINGERPRINT',
    'C2',
    'APT',
    'MALWARE',
    'BRUTEFORCE',
    'ANOMALY'
);

-- 域名类型枚举
CREATE TYPE domain_type_enum AS ENUM (
    'MALICIOUS',
    'BENIGN',
    'SUSPICIOUS',
    'WHITELIST',
    'BLACKLIST',
    'CDN'
);

-- 指纹类型枚举
CREATE TYPE fingerprint_type_enum AS ENUM (
    'HTTP',
    'SSL',
    'SSH',
    'FTP',
    'SMTP',
    'DNS',
    'TLS_JA3',
    'TLS_JA3S'
);

-- 标签来源枚举
CREATE TYPE label_source_enum AS ENUM (
    'SYSTEM',    -- 系统内置标签
    'RULE',      -- 规则标签（来自检测规则）
    'USER'       -- 用户自定义标签
);

-- 标签目标类型枚举
CREATE TYPE label_target_type_enum AS ENUM (
    'IP',           -- IP地址标签
    'APPLICATION',  -- 应用标签
    'DOMAIN',       -- 域名标签
    'CERTIFICATE',  -- 证书标签
    'SESSION',      -- 会话标签
    'FINGERPRINT',  -- 指纹标签
    'MAC',          -- MAC地址标签
    'PORT'          -- 端口标签
);

-- 标签分类枚举
CREATE TYPE label_category_enum AS ENUM (
    -- 实际使用的标签类别
    'THREAT',                        -- 威胁（533个标签）
    'KNOWLEDGE_BASE',                -- 知识库（390个标签）
    'HIGH_DIMENSION_LABEL',          -- 高维度标签（87个标签）
    'PROXY',                         -- 代理（56个标签）
    'ENCRYPTED_TRAFFIC_DETECTION',   -- 加密流量检测（22个标签）
    'LEGITIMACY',                    -- 合法性（22个标签）
    'BASIC_ATTRIBUTES',              -- 基础属性（11个标签）
    'APT',                           -- APT（5个标签）
    'BEHAVIOR_DESCRIPTION',          -- 行为描述（3个标签）
    'BEHAVIOR_DETECTION_MODULE',     -- 行为检测模块（2个标签）
    'COMMAND_CONTROL',               -- 命令与控制（1个标签）
    'FINGERPRINT_DESCRIPTION',       -- 指纹描述（1个标签）
    -- 证书标签类别
    'SECURITY',                      -- 安全
    'TRUST',                         -- 信任
    'USAGE',                         -- 使用
    'VALIDATION',                    -- 验证
    'MALICIOUS',                     -- 恶意
    -- 预留类别
    'REMOTE_CONTROL',                -- 远程控制
    'FUNCTION_DESCRIPTION',          -- 功能描述
    'ATTACK_INTRUSION',              -- 攻击入侵
    'IDENTITY_SPOOFING',             -- 身份欺骗
    'CIRCUMVENTION',                 -- 翻墙上网
    'MAN_IN_MIDDLE',                 -- 中间人
    'PRIVATE_DETECTION'              -- 私有检测
);

-- Cyber Kill Chain枚举（基于Lockheed Martin Cyber Kill Chain模型）
DROP TYPE IF EXISTS cyber_kill_chain_enum CASCADE;

CREATE TYPE cyber_kill_chain_enum AS ENUM (
    'RECONNAISSANCE',        -- 侦察探测
    'WEAPONIZATION',         -- 武器化
    'DELIVERY',              -- 投递
    'EXPLOITATION',          -- 漏洞利用
    'INSTALLATION',          -- 安装植入
    'COMMAND_AND_CONTROL',   -- 命令控制
    'ACTIONS_ON_OBJECTIVES', -- 目标行动
    'OTHER',                 -- 其他
    'UNKNOWN'                -- 未知
);

COMMENT ON TYPE cyber_kill_chain_enum IS 'Cyber Kill Chain枚举，基于Lockheed Martin标准杀伤链模型';

-- 规则来源枚举
DROP TYPE IF EXISTS rule_source_enum CASCADE;

CREATE TYPE rule_source_enum AS ENUM (
    'SYSTEM',  -- 系统内置 (0)
    'USER'     -- 用户自定义 (1)
);

-- 采集模式枚举
DROP TYPE IF EXISTS capture_mode_enum CASCADE;

CREATE TYPE capture_mode_enum AS ENUM (
    'SINGLE_PACKET',        -- 单包 (1)
    'CONNECTION',           -- 连接 (2)
    'SOURCE_IP',           -- 获取源IP (3)
    'DEST_IP',             -- 获取目的IP (4)
    'SOURCE_IP_PORT',      -- 获取源IP+Port (5)
    'DEST_IP_PORT',        -- 获取目的IP+Port (6)
    'SOURCE_DEST_IP',      -- 获取源IP+目的IP (7)
    'DNS_CLIENT_IP',       -- 获取DNS 客户端IP (8)
    'DNS_A_RECORD_IP',     -- 获取DNS A记录端IP (9)
    'DNS_CLIENT_A_RECORD'  -- 获取DNS 客户端+A记录 (10)
);

-- 过滤条件枚举
DROP TYPE IF EXISTS filter_criteria_enum CASCADE;

CREATE TYPE filter_criteria_enum AS ENUM (
    'PORT',                -- 按端口过滤 (0)
    'INTERNET_PROTOCOL',   -- 按互联网协议过滤 (1)
    'SUBNET'               -- 按子网过滤 (2)
);

-- ========================================
-- 系统元数据表
-- ========================================

-- Cyber Kill Chain详情表（提供枚举值的详细信息）
DROP TABLE IF EXISTS cyber_kill_chain_details CASCADE;

CREATE TABLE cyber_kill_chain_details (
    cyber_kill_chain cyber_kill_chain_enum PRIMARY KEY,
    chinese_name VARCHAR(64) NOT NULL,
    english_name VARCHAR(64) NOT NULL,
    description TEXT DEFAULT '',
    typical_techniques TEXT[] DEFAULT '{}',
    defense_recommendations TEXT[] DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cyber_kill_chain_details IS 'Cyber Kill Chain详情表，提供枚举值的详细信息';
COMMENT ON COLUMN cyber_kill_chain_details.cyber_kill_chain IS 'Cyber Kill Chain阶段枚举值（主键）';
COMMENT ON COLUMN cyber_kill_chain_details.chinese_name IS '阶段中文名称';
COMMENT ON COLUMN cyber_kill_chain_details.english_name IS '阶段英文名称';
COMMENT ON COLUMN cyber_kill_chain_details.description IS '阶段详细描述';
COMMENT ON COLUMN cyber_kill_chain_details.typical_techniques IS '典型攻击技术列表';
COMMENT ON COLUMN cyber_kill_chain_details.defense_recommendations IS '防护建议列表';
COMMENT ON COLUMN cyber_kill_chain_details.created_at IS '创建时间';
COMMENT ON COLUMN cyber_kill_chain_details.updated_at IS '更新时间';

-- ========================================
-- Cyber Kill Chain详情数据初始化
-- ========================================

-- 插入Cyber Kill Chain详情数据
INSERT INTO cyber_kill_chain_details (
    cyber_kill_chain,
    chinese_name,
    english_name,
    description,
    typical_techniques,
    defense_recommendations
) VALUES
(
    'RECONNAISSANCE',
    '侦察探测',
    'Reconnaissance',
    '攻击者收集目标组织的信息，包括网络架构、系统配置、人员信息等。信息一般通过互联网进行收集（内容包括网站、邮箱、电话、社会工程学等一切可能相关的情报）',
    ARRAY['网络扫描', '端口扫描', '域名查询', '社会工程学', '开源情报收集'],
    ARRAY['加强网络边界监控', '限制信息泄露', '部署蜜罐系统', '监控异常扫描活动']
),
(
    'WEAPONIZATION',
    '武器化',
    'Weaponization',
    '攻击者创建恶意载荷，将漏洞利用代码与恶意软件结合形成武器。这个阶段通常在攻击者的基础设施中进行，难以直接检测',
    ARRAY['恶意软件开发', '漏洞利用工具', '载荷封装', '免杀处理', '木马制作'],
    ARRAY['威胁情报收集', '恶意软件检测', '沙箱分析', '特征库更新']
),
(
    'DELIVERY',
    '投递',
    'Delivery',
    '攻击者通过各种方式将恶意载荷传输到目标环境。这是攻击者首次与目标系统接触的阶段',
    ARRAY['钓鱼邮件', '恶意附件', '水坑攻击', 'USB投递', '供应链攻击'],
    ARRAY['邮件安全网关', 'Web内容过滤', 'USB端口控制', '用户安全培训']
),
(
    'EXPLOITATION',
    '漏洞利用',
    'Exploitation',
    '攻击者利用系统或应用程序漏洞执行恶意代码。一般会利用应用程序或操作系统的漏洞或缺陷等',
    ARRAY['漏洞利用', '缓冲区溢出', '代码注入', '权限提升', '零日攻击'],
    ARRAY['及时补丁管理', '漏洞扫描评估', '入侵检测系统', '行为监控分析']
),
(
    'INSTALLATION',
    '安装植入',
    'Installation',
    '攻击者在目标系统设置木马、后门等，一定期限内在目标系统营造活动环境的阶段。攻击者在受害者系统上安装恶意软件、后门或其他持久化机制',
    ARRAY['后门安装', 'Webshell植入', '服务安装', '注册表修改', '计划任务'],
    ARRAY['终端安全防护', '文件完整性监控', '权限管理控制', '系统基线检查']
),
(
    'COMMAND_AND_CONTROL',
    '命令控制',
    'Command and Control',
    '攻击者建立目标系统攻击路径的阶段。一般使用自动和手工相结合的方式进行，一旦攻击路径确立后，攻击者将能够控制目标系统。攻击者建立与被感染系统的通信通道，实现远程控制',
    ARRAY['C2通信', 'DNS隧道', 'HTTP/HTTPS通信', '加密通道', '代理通信'],
    ARRAY['网络流量监控', 'DNS安全防护', '代理服务器控制', '异常通信检测']
),
(
    'ACTIONS_ON_OBJECTIVES',
    '目标行动',
    'Actions on Objectives',
    '攻击者达到预期目标的阶段。攻击目标呈现多样化，可能包括侦察、敏感信息收集、数据破坏、系统摧毁等。攻击者执行最终的攻击目标，如数据窃取、系统破坏、横向移动等',
    ARRAY['数据窃取', '数据销毁', '横向移动', '权限维持', '勒索加密'],
    ARRAY['数据丢失防护', '特权账户监控', '横向移动检测', '应急响应处置']
),
(
    'UNKNOWN',
    '未知',
    'Unknown',
    '无法确定攻击阶段的告警类型',
    ARRAY['未分类告警'],
    ARRAY['告警分析', '威胁分类', '专家研判']
);

-- 检测器配置表
DROP TABLE IF EXISTS detector_config CASCADE;

CREATE TABLE detector_config (
    id BIGSERIAL PRIMARY KEY,
    detector_name VARCHAR(255) UNIQUE NOT NULL,
    detector_type detector_type_enum NOT NULL,
    description TEXT,
    config_json JSONB NOT NULL,
    thresholds JSONB,
    enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    version VARCHAR(20) DEFAULT '1.0'
);

COMMENT ON TABLE detector_config IS '检测器配置表';

-- 创建索引
CREATE INDEX idx_detector_config_name ON detector_config (detector_name);
CREATE INDEX idx_detector_config_type ON detector_config (detector_type);
CREATE INDEX idx_detector_config_enabled ON detector_config (enabled);

-- 协议表（系统元数据）
-- 网络协议表
DROP TABLE IF EXISTS network_protocols CASCADE;

CREATE TABLE network_protocols (
    id INTEGER PRIMARY KEY,
    protocol_name TEXT NOT NULL,
    display_name TEXT,
    category TEXT,
    description TEXT,
    protocol_type INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE network_protocols IS '网络协议表（系统元数据）';
COMMENT ON COLUMN network_protocols.id IS '协议ID';
COMMENT ON COLUMN network_protocols.protocol_name IS '协议名称';
COMMENT ON COLUMN network_protocols.display_name IS '显示名称';
COMMENT ON COLUMN network_protocols.category IS '协议分类';
COMMENT ON COLUMN network_protocols.description IS '协议描述';
COMMENT ON COLUMN network_protocols.protocol_type IS '协议类型 1 为连接 2 为单包 3 tcp/udp负载';

-- 协议表索引
CREATE INDEX idx_network_protocols_id ON network_protocols (id);
CREATE INDEX idx_network_protocols_protocol_name ON network_protocols (protocol_name);
CREATE INDEX idx_network_protocols_category ON network_protocols (category);
CREATE INDEX idx_network_protocols_protocol_type ON network_protocols (protocol_type);

-- 互联网协议表
DROP TABLE IF EXISTS internet_protocols CASCADE;

CREATE TABLE internet_protocols (
    protocol_number INTEGER PRIMARY KEY,
    keyword VARCHAR(255) NOT NULL,
    protocol_description VARCHAR(500) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE internet_protocols IS '互联网协议表（IANA分配的IP协议号）';
COMMENT ON COLUMN internet_protocols.protocol_number IS '协议号（0-255）';
COMMENT ON COLUMN internet_protocols.keyword IS '协议关键字';
COMMENT ON COLUMN internet_protocols.protocol_description IS '协议描述';

CREATE INDEX idx_internet_protocols_protocol_number ON internet_protocols (protocol_number);
CREATE INDEX idx_internet_protocols_keyword ON internet_protocols (keyword);

-- ========================================
-- 统一标签管理表
-- ========================================

-- 统一标签表
CREATE TABLE labels (
    -- 基础标识字段
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,                    -- 标签名称（英文标识，如 "malicious_ip"）
    display_name VARCHAR(255) NOT NULL,            -- 显示名称（用户友好名称，如 "恶意IP"）
    description TEXT,                              -- 详细描述
    remark TEXT,                                   -- 备注信息

    -- 分类字段
    target_type label_target_type_enum NOT NULL,  -- 标签目标类型
    category label_category_enum,                  -- 标签类别（威胁、合法性等）
    source label_source_enum NOT NULL DEFAULT 'SYSTEM', -- 标签来源

    -- 评分字段（0-100）
    threat_level INTEGER DEFAULT 0 CHECK (
        threat_level >= 0 AND threat_level <= 100
    ),
    trust_level INTEGER DEFAULT 0 CHECK (
        trust_level >= 0 AND trust_level <= 100
    ),
    default_threat_level INTEGER DEFAULT 0 CHECK (
        default_threat_level >= 0 AND default_threat_level <= 100
    ),
    default_trust_level INTEGER DEFAULT 0 CHECK (
        default_trust_level >= 0 AND default_trust_level <= 100
    ),

    -- 业务字段
    cyber_kill_chain cyber_kill_chain_enum, -- Cyber Kill Chain阶段
    color VARCHAR(7) DEFAULT '#666666',     -- 标签颜色（十六进制，用于UI显示）
    sort_order INTEGER DEFAULT 0,          -- 排序顺序

    -- 状态字段
    is_active BOOLEAN NOT NULL DEFAULT true, -- 是否激活
    version INTEGER NOT NULL DEFAULT 1,      -- 版本号（乐观锁）

    -- 审计字段
    created_by INTEGER,                       -- 创建者用户ID
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,

    -- 约束
    CONSTRAINT uk_labels_name_target_type UNIQUE (name, target_type), -- 同类型下标签名称唯一
    CONSTRAINT ck_labels_threat_trust CHECK (threat_level + trust_level <= 100) -- 威胁+信任不超过100
);

-- 创建索引
CREATE INDEX idx_labels_target_type ON labels (target_type);
CREATE INDEX idx_labels_category ON labels (category);
CREATE INDEX idx_labels_is_active ON labels (is_active);
CREATE INDEX idx_labels_created_at ON labels (created_at);
CREATE INDEX idx_labels_target_category ON labels (target_type, category);
CREATE INDEX idx_labels_source ON labels (source);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_labels_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    NEW.version = OLD.version + 1;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_labels_updated_at
    BEFORE UPDATE ON labels
    FOR EACH ROW
    EXECUTE FUNCTION update_labels_updated_at();

-- 表注释
COMMENT ON TABLE labels IS '统一标签表，支持多种目标类型的标签管理';
COMMENT ON COLUMN labels.id IS '标签唯一标识';
COMMENT ON COLUMN labels.name IS '标签名称（英文标识）';
COMMENT ON COLUMN labels.display_name IS '显示名称（用户友好名称）';
COMMENT ON COLUMN labels.description IS '标签详细描述';
COMMENT ON COLUMN labels.remark IS '备注信息';
COMMENT ON COLUMN labels.target_type IS '标签目标类型';
COMMENT ON COLUMN labels.category IS '标签类别';
COMMENT ON COLUMN labels.source IS '标签来源：SYSTEM-系统内置, USER-用户自定义';
COMMENT ON COLUMN labels.threat_level IS '威胁等级（0-100）';
COMMENT ON COLUMN labels.trust_level IS '信任等级（0-100）';
COMMENT ON COLUMN labels.default_threat_level IS '默认威胁等级（0-100）';
COMMENT ON COLUMN labels.default_trust_level IS '默认信任等级（0-100）';
COMMENT ON COLUMN labels.cyber_kill_chain IS '攻击阶段';
COMMENT ON COLUMN labels.color IS '标签颜色（十六进制）';
COMMENT ON COLUMN labels.sort_order IS '排序顺序';
COMMENT ON COLUMN labels.is_active IS '是否激活';
COMMENT ON COLUMN labels.version IS '版本号（乐观锁）';
COMMENT ON COLUMN labels.created_by IS '创建者用户ID';
COMMENT ON COLUMN labels.created_at IS '创建时间';
COMMENT ON COLUMN labels.updated_at IS '更新时间';

-- ========================================
-- 系统配置和元数据表
-- ========================================

-- 应用协议信息表
DROP TABLE IF EXISTS application_protocol_info CASCADE;

CREATE TABLE application_protocol_info (
    id SERIAL PRIMARY KEY,
    port INTEGER,
    appid INTEGER,
    Ippro INTEGER,
    remark TEXT NOT NULL
);

COMMENT ON TABLE application_protocol_info IS '应用协议信息表';
COMMENT ON COLUMN application_protocol_info.port IS '端口';
COMMENT ON COLUMN application_protocol_info.appid IS 'app ID';
COMMENT ON COLUMN application_protocol_info.Ippro IS '17 udp 6 tcp';

-- DNS服务器配置表
DROP TABLE IF EXISTS dns_server_config CASCADE;

CREATE TABLE dns_server_config (
    tkey BIGINT PRIMARY KEY,
    ip VARCHAR(64) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    source INTEGER NOT NULL,
    client_dis VARCHAR(2048) NOT NULL,
    task_id INTEGER NOT NULL
);

COMMENT ON COLUMN dns_server_config.ip IS 'IP';
COMMENT ON COLUMN dns_server_config.source IS '来源';
COMMENT ON COLUMN dns_server_config.client_dis IS 'client';
COMMENT ON COLUMN dns_server_config.task_id IS '任务ID';

-- 系统字典表
DROP TABLE IF EXISTS system_dictionary CASCADE;

CREATE TABLE system_dictionary (
    id SERIAL PRIMARY KEY,
    valset_id VARCHAR(255),
    val_id VARCHAR(255),
    value VARCHAR(255)
);

COMMENT ON TABLE system_dictionary IS '系统字典表';

-- 系统状态配置表
DROP TABLE IF EXISTS system_state_config CASCADE;

CREATE TABLE system_state_config (
    id BIGSERIAL PRIMARY KEY,
    type_name VARCHAR(255) NOT NULL,
    type_value TEXT NOT NULL,
    field_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE system_state_config IS '系统状态配置表';
COMMENT ON COLUMN system_state_config.type_name IS '功能状态简写';
COMMENT ON COLUMN system_state_config.type_value IS '功能状态详细说明';
COMMENT ON COLUMN system_state_config.field_name IS '字段名称';

-- 证书检测模型映射表
DROP TABLE IF EXISTS cert_label_model_mapping CASCADE;

CREATE TABLE cert_label_model_mapping (
    id BIGSERIAL PRIMARY KEY,
    label_id INTEGER REFERENCES labels (id),
    model_id BIGINT NOT NULL,
    model_name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE cert_label_model_mapping IS '证书检测模型映射表';

-- 注意：tb_tag_info 表已经重构为统一的 labels 表
-- 如果需要兼容旧的 tb_tag_info 表结构，可以创建视图：
-- CREATE VIEW tb_tag_info AS SELECT id as tag_id, name as tag_text, ... FROM labels;

-- 元数据定义表
DROP TABLE IF EXISTS metadata_definitions CASCADE;

CREATE TABLE metadata_definitions (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) UNIQUE NOT NULL,
    metadata_name VARCHAR(255) NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    description TEXT,
    default_value TEXT,
    validation_rules JSONB,
    category VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE metadata_definitions IS '元数据定义表';
COMMENT ON COLUMN metadata_definitions.data_type IS '数据类型: STRING, INTEGER, BOOLEAN, JSON, ARRAY';

-- 元数据值表
DROP TABLE IF EXISTS metadata_values CASCADE;

CREATE TABLE metadata_values (
    id SERIAL PRIMARY KEY,
    metadata_key VARCHAR(255) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    entity_id VARCHAR(255) NOT NULL,
    metadata_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (
        metadata_key,
        entity_type,
        entity_id
    )
);

COMMENT ON TABLE metadata_values IS '元数据值表';
COMMENT ON COLUMN metadata_values.entity_type IS '实体类型';
COMMENT ON COLUMN metadata_values.entity_id IS '实体ID';

-- ========================================
-- 系统元数据表索引
-- ========================================

-- 应用协议信息表索引
CREATE INDEX idx_application_protocol_info_port ON application_protocol_info (port);
CREATE INDEX idx_application_protocol_info_appid ON application_protocol_info (appid);

-- DNS服务器配置表索引
CREATE INDEX idx_dns_server_config_ip ON dns_server_config (ip);
CREATE INDEX idx_dns_server_config_task_id ON dns_server_config (task_id);

-- 元数据定义表索引
CREATE INDEX idx_metadata_definitions_key ON metadata_definitions (metadata_key);
CREATE INDEX idx_metadata_definitions_category ON metadata_definitions (category);

-- 元数据值表索引
CREATE INDEX idx_metadata_values_key ON metadata_values (metadata_key);
CREATE INDEX idx_metadata_values_entity ON metadata_values (entity_type, entity_id);

-- ========================================
-- 系统元数据表触发器
-- ========================================

-- 为元数据表创建更新时间触发器
CREATE TRIGGER update_metadata_definitions_updated_at
    BEFORE UPDATE ON metadata_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_metadata_values_updated_at
    BEFORE UPDATE ON metadata_values
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 统一标签关联表
-- ========================================

-- 统一标签关联表 - 支持所有类型的实体与标签的关联
DROP TABLE IF EXISTS entity_labels CASCADE;

CREATE TABLE entity_labels (
    id SERIAL PRIMARY KEY,
    entity_type label_target_type_enum NOT NULL, -- 实体类型（IP、CERTIFICATE、SESSION等）
    entity_id VARCHAR(255) NOT NULL,             -- 实体标识符（如IP地址、证书哈希、会话ID等）
    label_id INTEGER NOT NULL REFERENCES labels (id) ON DELETE CASCADE, -- 引用统一标签表
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,                          -- 创建者用户ID
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,                          -- 更新者用户ID
    UNIQUE (entity_type, entity_id, label_id)   -- 同一实体不能重复关联同一个标签
);

COMMENT ON TABLE entity_labels IS '统一标签关联表 - 存储所有类型实体与标签的关联关系';
COMMENT ON COLUMN entity_labels.entity_type IS '实体类型，使用label_target_type_enum枚举';
COMMENT ON COLUMN entity_labels.entity_id IS '实体标识符，如IP地址、证书哈希、会话ID等';
COMMENT ON COLUMN entity_labels.label_id IS '标签ID，引用labels表';
COMMENT ON COLUMN entity_labels.created_by IS '关联创建者用户ID';
COMMENT ON COLUMN entity_labels.updated_by IS '关联更新者用户ID';

-- 创建索引以支持高效查询
CREATE INDEX idx_entity_labels_entity ON entity_labels (entity_type, entity_id);
CREATE INDEX idx_entity_labels_label_id ON entity_labels (label_id);
CREATE INDEX idx_entity_labels_created_at ON entity_labels (created_at);
CREATE INDEX idx_entity_labels_created_by ON entity_labels (created_by);
CREATE INDEX idx_entity_labels_type_label ON entity_labels (entity_type, label_id);

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 创建更新时间触发器
CREATE TRIGGER update_entity_labels_updated_at
    BEFORE UPDATE ON entity_labels
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 从 02-th_analysis.sql 迁移的元数据相关表
-- ========================================

-- 告警类型表
DROP TABLE IF EXISTS alarm_types CASCADE;

CREATE TABLE alarm_types (
    id SERIAL PRIMARY KEY,
    type_name VARCHAR(255) NOT NULL,
    type_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    severity_level INTEGER DEFAULT 1,
    category VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE alarm_types IS '告警类型表';
COMMENT ON COLUMN alarm_types.type_name IS '告警类型名称';
COMMENT ON COLUMN alarm_types.type_code IS '告警类型代码';
COMMENT ON COLUMN alarm_types.description IS '告警类型描述';
COMMENT ON COLUMN alarm_types.severity_level IS '严重程度等级（1-5）';
COMMENT ON COLUMN alarm_types.category IS '告警类别';
COMMENT ON COLUMN alarm_types.is_active IS '是否激活';

-- 为新增表创建索引
CREATE INDEX idx_alarm_types_type_code ON alarm_types (type_code);
CREATE INDEX idx_alarm_types_category ON alarm_types (category);
CREATE INDEX idx_alarm_types_is_active ON alarm_types (is_active);

-- 为新增表创建更新时间触发器
CREATE TRIGGER update_alarm_types_updated_at
    BEFORE UPDATE ON alarm_types
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
